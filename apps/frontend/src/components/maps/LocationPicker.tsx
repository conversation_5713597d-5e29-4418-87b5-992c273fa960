"use client";

import React, { useState, useEffect, useTransition } from "react";
import { cn } from "@/utils";
import Input from "@/components/ui/Input";
import Button from "@/components/ui/Button";
import { Card } from "@/components/ui/Card";
import { MapPinIcon, CheckIcon, XMarkIcon } from "@heroicons/react/24/outline";
import {
    searchLieuxAction,
    reverseGeocodeAction,
    createLocationFromGeocodedDataAction,
} from "@/lib/actions";
import { Lieu } from "@/types";

interface LocationPickerProps {
    onLocationSelect: (location: Lieu | null) => void;
    defaultValue?: Lieu;
    placeholder?: string;
    className?: string;
    required?: boolean;
    disabled?: boolean;
}

const LocationPicker: React.FC<LocationPickerProps> = ({
    onLocationSelect,
    defaultValue,
    placeholder = "Entrez votre ville ou adresse...",
    className,
    required = false,
    disabled = false,
}) => {
    const [query, setQuery] = useState(defaultValue?.nom || "");
    const [suggestions, setSuggestions] = useState<Lieu[]>([]);
    const [selectedLocation, setSelectedLocation] = useState<Lieu | null>(defaultValue || null);
    const [isLoading, setIsLoading] = useState(false);
    const [showSuggestions, setShowSuggestions] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const [isPending, startTransition] = useTransition();

    // Debounced search for locations
    useEffect(() => {
        if (query.length < 3) {
            setSuggestions([]);
            return;
        }

        const timer = setTimeout(() => {
            searchLocations(query);
        }, 300);

        return () => clearTimeout(timer);
    }, [query]);

    const searchLocations = async (searchQuery: string) => {
        if (!searchQuery.trim()) return;

        setError(null);

        startTransition(async () => {
            try {
                const result = await searchLieuxAction(searchQuery);

                if (result.success && result.data) {
                    setSuggestions(result.data);
                    setShowSuggestions(true);
                } else {
                    setError(result.error || "Erreur lors de la recherche");
                    setSuggestions([]);
                }
            } catch (error) {
                console.error("Location search error:", error);
                setError("Erreur lors de la recherche de lieu");
                setSuggestions([]);
            }
        });
    };

    const handleLocationSelect = (location: Lieu) => {
        setSelectedLocation(location);
        setQuery(location.nom);
        setShowSuggestions(false);
        onLocationSelect(location);
    };

    const handleClear = () => {
        setQuery("");
        setSelectedLocation(null);
        setSuggestions([]);
        setShowSuggestions(false);
        onLocationSelect(null);
    };

    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const value = e.target.value;
        setQuery(value);

        if (value !== selectedLocation?.nom) {
            setSelectedLocation(null);
            onLocationSelect(null);
        }
    };

    // Use browser geolocation API
    // This will try to find the most appropriate existing location (ville, departement, region)
    // rather than creating very specific locations for each address
    const getCurrentLocation = () => {
        if (!navigator.geolocation) {
            setError("La géolocalisation n'est pas supportée par votre navigateur");
            return;
        }

        setIsLoading(true);
        navigator.geolocation.getCurrentPosition(
            (position) => {
                // Use transition for the async geocoding
                startTransition(async () => {
                    try {
                        // Get address from coordinates using server action
                        const result = await reverseGeocodeAction(
                            position.coords.latitude,
                            position.coords.longitude,
                        );

                        if (result.success && result.data) {
                            const addressData = result.data;

                            // Try to find or create location in database
                            const locationResult = await createLocationFromGeocodedDataAction({
                                nom: addressData.nom,
                                type:
                                    addressData.type === "commune"
                                        ? "commune"
                                        : addressData.type === "road"
                                          ? "adresse"
                                          : "point",
                                niveau:
                                    addressData.type === "commune"
                                        ? "ville"
                                        : addressData.type === "road" ||
                                            addressData.type === "adresse"
                                          ? "rue"
                                          : "ville",
                                coordonnees: {
                                    lat: position.coords.latitude,
                                    lng: position.coords.longitude,
                                },
                                adresse_complete: addressData.adresse_complete,
                                rue: addressData.rue,
                                ville: addressData.ville,
                                code_postal: addressData.code_postal,
                                departement: addressData.departement,
                                region: addressData.region,
                                pays: (addressData.pays || "FR").toUpperCase().slice(0, 2),
                            });

                            if (locationResult.success && locationResult.data) {
                                // Use the location from database (existing or newly created)
                                handleLocationSelect(locationResult.data);
                            } else {
                                // If no appropriate location found, show message
                                setError(
                                    "La localisation doit être au moins au niveau de la ville",
                                );
                                // Still show the address info to the user
                                setQuery(addressData.adresse_complete || addressData.nom);
                            }

                            setIsLoading(false);
                        } else {
                            setError(
                                result.error ||
                                    "Impossible de déterminer l'adresse de votre position",
                            );
                            setIsLoading(false);
                        }
                    } catch (error) {
                        setError("Impossible de déterminer votre localisation");
                        setIsLoading(false);
                    }
                });
            },
            (error) => {
                setError("Accès à la localisation refusé");
                setIsLoading(false);
            },
            {
                enableHighAccuracy: true,
                timeout: 10000,
                maximumAge: 300000, // 5 minutes
            },
        );
    };

    const getLocationIcon = (type: string) => {
        switch (type) {
            case "commune":
                return "🏙️";
            case "departement":
                return "📍";
            case "region":
                return "🗺️";
            case "pays":
                return "🌍";
            case "point_interet":
                return "📌";
            case "zone":
                return "🌐";
            case "road":
            case "adresse":
                return "🏠";
            default:
                return "📍";
        }
    };

    return (
        <div className={cn("relative", className)}>
            <div className="relative">
                <MapPinIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-muted-foreground" />

                <Input
                    type="text"
                    value={query}
                    onChange={handleInputChange}
                    onFocus={() => suggestions.length > 0 && setShowSuggestions(true)}
                    onBlur={() => {
                        // Delay to allow clicking on suggestions
                        setTimeout(() => setShowSuggestions(false), 200);
                    }}
                    placeholder={placeholder}
                    className={cn(
                        "pl-10 pr-20",
                        selectedLocation && "border-success",
                        error && "border-destructive",
                    )}
                    required={required}
                    disabled={disabled}
                />

                <div className="absolute right-2 top-1/2 transform -translate-y-1/2 flex gap-1">
                    {selectedLocation && (
                        <button
                            onClick={handleClear}
                            className="p-1 text-muted-foreground hover:text-foreground transition-colors"
                            disabled={disabled}
                        >
                            <XMarkIcon className="h-4 w-4" />
                        </button>
                    )}

                    <Button
                        variant="ghost"
                        size="xs"
                        onClick={getCurrentLocation}
                        disabled={disabled || isLoading}
                        className="h-6 w-6 p-0"
                        title="Utiliser ma position actuelle"
                    >
                        🎯
                    </Button>
                </div>
            </div>

            {/* Loading indicator */}
            {(isLoading || isPending) && (
                <div className="absolute right-12 top-1/2 transform -translate-y-1/2">
                    <div className="animate-spin h-4 w-4 border-2 border-primary border-t-transparent rounded-full"></div>
                </div>
            )}

            {/* Error message */}
            {error && <p className="mt-1 text-xs text-destructive">{error}</p>}

            {/* Selected location display */}
            {selectedLocation && (
                <div className="mt-2 p-2 bg-success/10 border border-success/20 rounded-lg flex items-center gap-2">
                    <CheckIcon className="h-4 w-4 text-success flex-shrink-0" />
                    <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-success truncate">
                            {selectedLocation.adresse_complete || selectedLocation.nom}
                        </p>
                        {selectedLocation.rue && (
                            <p className="text-xs text-success/80">
                                <span className="text-lg mr-1">🏠</span>
                                {selectedLocation.rue}
                            </p>
                        )}
                        {selectedLocation.coordonnees && (
                            <p className="text-xs text-success/70">
                                {selectedLocation.coordonnees.lat.toFixed(6)},{" "}
                                {selectedLocation.coordonnees.lng.toFixed(6)}
                            </p>
                        )}
                    </div>
                </div>
            )}

            {/* Suggestions dropdown */}
            {showSuggestions && suggestions.length > 0 && (
                <Card className="absolute z-10 w-full mt-1 max-h-64 overflow-y-auto shadow-lg">
                    <div className="py-1">
                        {suggestions.map((location) => (
                            <button
                                key={location.documentId}
                                onClick={() => handleLocationSelect(location)}
                                className="w-full px-3 py-2.5 text-left hover:bg-secondary/50 transition-colors flex items-center gap-3"
                            >
                                <span className="text-lg">{getLocationIcon(location.type)}</span>
                                <div className="flex-1 min-w-0">
                                    <p className="text-sm font-medium text-foreground truncate">
                                        {location.nom}
                                    </p>
                                    {location.rue && (
                                        <p className="text-xs font-medium text-muted-foreground truncate">
                                            {location.rue}
                                        </p>
                                    )}
                                    <p className="text-xs text-muted-foreground truncate">
                                        {location.adresse_complete ||
                                            [location.ville, location.departement, location.region]
                                                .filter(Boolean)
                                                .join(", ")}
                                    </p>
                                </div>
                                <span className="text-xs text-muted-foreground capitalize bg-secondary px-2 py-1 rounded">
                                    {location.type}
                                </span>
                            </button>
                        ))}
                    </div>
                </Card>
            )}
        </div>
    );
};

export default LocationPicker;
