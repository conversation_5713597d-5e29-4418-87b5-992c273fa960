"use client";

import { useEffect } from "react";
import { useNotificationStore } from "@/stores/notificationStore";

interface NotificationLoaderProps {
    initialNotifications?: any[];
}

export function NotificationLoader({ initialNotifications = [] }: NotificationLoaderProps) {
    const { setNotifications, setConnectionStatus } = useNotificationStore();

    useEffect(() => {
        // Load initial notifications into the store
        if (initialNotifications.length > 0) {
            const formattedNotifications = initialNotifications.map((notif) => ({
                id: notif.id || notif.documentId,
                title: notif.title,
                message: notif.message,
                type: notif.type,
                timestamp: new Date(notif.createdAt),
                read: notif.read,
                actionUrl: notif.action_url || notif.metadata?.actionUrl,
                actionLabel: notif.action_label || notif.metadata?.actionLabel,
                expressionId: notif.expression?.documentId || notif.metadata?.expressionId,
                userId: notif.metadata?.userId,
            }));

            setNotifications(formattedNotifications);
        }

        // Mark as connected when component mounts
        setConnectionStatus(true);

        return () => {
            setConnectionStatus(false);
        };
    }, [initialNotifications]);

    return null;
}
