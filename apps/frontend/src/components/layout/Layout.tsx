"use client";

import React from "react";
import Header from "./Header";
import Footer from "./Footer";
import RealtimeProvider from "@/components/providers/RealtimeProvider";

interface LayoutProps {
    children: React.ReactNode;
    showHeader?: boolean;
    showFooter?: boolean;
    className?: string;
}

const Layout: React.FC<LayoutProps> = ({
    children,
    showHeader = true,
    showFooter = true,
    className = "",
}) => {
    return (
        <RealtimeProvider>
            <div className="min-h-screen flex flex-col bg-gray-50">
                {showHeader && <Header />}

                <main className={`flex-1 ${className}`}>{children}</main>

                {showFooter && <Footer />}
            </div>
        </RealtimeProvider>
    );
};

export default Layout;
