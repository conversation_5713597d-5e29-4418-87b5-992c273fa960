"use client";

import React from "react";
import { cn } from "@/utils";
import Button from "@/components/ui/Button";
import { Card } from "@/components/ui/Card";
import {
    ExclamationTriangleIcon,
    WifiIcon,
    ServerStackIcon,
    MagnifyingGlassIcon,
    ArrowPathIcon,
    HomeIcon,
    ShieldExclamationIcon,
} from "@heroicons/react/24/outline";
import Link from "next/link";

interface ErrorStateProps {
    type?: "error" | "network" | "server" | "empty" | "notFound" | "unauthorized" | "forbidden";
    title?: string;
    message?: string;
    onRetry?: () => void;
    onGoHome?: () => void;
    children?: React.ReactNode;
    className?: string;
    showIcon?: boolean;
    actions?: React.ReactNode;
}

const ErrorState: React.FC<ErrorStateProps> = ({
    type = "error",
    title,
    message,
    onRetry,
    onGoHome,
    children,
    className,
    showIcon = true,
    actions,
}) => {
    const getErrorConfig = () => {
        switch (type) {
            case "network":
                return {
                    icon: WifiIcon,
                    iconColor: "text-warning",
                    iconBg: "bg-warning/10",
                    defaultTitle: "Problème de connexion",
                    defaultMessage: "Vérifiez votre connexion internet et réessayez.",
                };
            case "server":
                return {
                    icon: ServerStackIcon,
                    iconColor: "text-destructive",
                    iconBg: "bg-destructive/10",
                    defaultTitle: "Erreur serveur",
                    defaultMessage:
                        "Nos serveurs rencontrent des difficultés. Veuillez réessayer plus tard.",
                };
            case "empty":
                return {
                    icon: MagnifyingGlassIcon,
                    iconColor: "text-muted-foreground",
                    iconBg: "bg-secondary",
                    defaultTitle: "Aucun résultat",
                    defaultMessage: "Aucun élément trouvé avec les critères de recherche actuels.",
                };
            case "notFound":
                return {
                    icon: MagnifyingGlassIcon,
                    iconColor: "text-muted-foreground",
                    iconBg: "bg-secondary",
                    defaultTitle: "Page introuvable",
                    defaultMessage: "La page que vous recherchez n'existe pas ou a été déplacée.",
                };
            case "unauthorized":
                return {
                    icon: ShieldExclamationIcon,
                    iconColor: "text-warning",
                    iconBg: "bg-warning/10",
                    defaultTitle: "Accès non autorisé",
                    defaultMessage: "Vous devez vous connecter pour accéder à cette page.",
                };
            case "forbidden":
                return {
                    icon: ShieldExclamationIcon,
                    iconColor: "text-destructive",
                    iconBg: "bg-destructive/10",
                    defaultTitle: "Accès interdit",
                    defaultMessage:
                        "Vous n'avez pas les permissions nécessaires pour accéder à cette page.",
                };
            default:
                return {
                    icon: ExclamationTriangleIcon,
                    iconColor: "text-destructive",
                    iconBg: "bg-destructive/10",
                    defaultTitle: "Une erreur s'est produite",
                    defaultMessage: "Quelque chose s'est mal passé. Veuillez réessayer.",
                };
        }
    };

    const config = getErrorConfig();
    const IconComponent = config.icon;

    const renderDefaultActions = () => {
        if (actions) return actions;

        const defaultActions = [];

        if (onRetry) {
            defaultActions.push(
                <Button
                    key="retry"
                    variant="outline"
                    onClick={onRetry}
                    icon={<ArrowPathIcon className="h-4 w-4" />}
                >
                    Réessayer
                </Button>,
            );
        }

        if (onGoHome) {
            defaultActions.push(
                <Button
                    key="home"
                    variant="primary"
                    onClick={onGoHome}
                    icon={<HomeIcon className="h-4 w-4" />}
                >
                    Retour à l'accueil
                </Button>,
            );
        }

        // Default actions based on error type
        if (defaultActions.length === 0) {
            switch (type) {
                case "unauthorized":
                    defaultActions.push(
                        <Button key="login" variant="primary" as={Link} href="/auth/login">
                            Se connecter
                        </Button>,
                    );
                    break;
                case "notFound":
                    defaultActions.push(
                        <Button
                            key="home"
                            variant="primary"
                            icon={<HomeIcon className="h-4 w-4" />}
                            as={Link}
                            href="/"
                        >
                            Retour à l'accueil
                        </Button>,
                    );
                    break;
                case "empty":
                    // No default actions for empty state
                    break;
                default:
                    defaultActions.push(
                        <Button
                            key="reload"
                            variant="primary"
                            onClick={() => window.location.reload()}
                            icon={<ArrowPathIcon className="h-4 w-4" />}
                        >
                            Actualiser la page
                        </Button>,
                    );
                    break;
            }
        }

        return defaultActions.length > 0 ? (
            <div className="flex flex-col sm:flex-row gap-3 justify-center">{defaultActions}</div>
        ) : null;
    };

    return (
        <div className={cn("flex items-center justify-center p-8", className)}>
            <Card className="w-full max-w-md p-8 text-center">
                {showIcon && (
                    <div
                        className={cn(
                            "w-16 h-16 rounded-full mx-auto mb-6 flex items-center justify-center",
                            config.iconBg,
                        )}
                    >
                        <IconComponent className={cn("h-8 w-8", config.iconColor)} />
                    </div>
                )}

                <h2 className="text-2xl font-bold text-foreground mb-4">
                    {title || config.defaultTitle}
                </h2>

                <p className="text-muted-foreground mb-6">{message || config.defaultMessage}</p>

                {children && <div className="mb-6">{children}</div>}

                {renderDefaultActions()}
            </Card>
        </div>
    );
};

// Convenience components for common error states
export const NetworkError: React.FC<Omit<ErrorStateProps, "type">> = (props) => (
    <ErrorState type="network" {...props} />
);

export const ServerError: React.FC<Omit<ErrorStateProps, "type">> = (props) => (
    <ErrorState type="server" {...props} />
);

export const EmptyState: React.FC<Omit<ErrorStateProps, "type">> = (props) => (
    <ErrorState type="empty" {...props} />
);

export const NotFoundError: React.FC<Omit<ErrorStateProps, "type">> = (props) => (
    <ErrorState type="notFound" {...props} />
);

export const UnauthorizedError: React.FC<Omit<ErrorStateProps, "type">> = (props) => (
    <ErrorState type="unauthorized" {...props} />
);

export const ForbiddenError: React.FC<Omit<ErrorStateProps, "type">> = (props) => (
    <ErrorState type="forbidden" {...props} />
);

export default ErrorState;
