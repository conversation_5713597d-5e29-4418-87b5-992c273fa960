import React from "react";

interface LoadingSpinnerProps {
    size?: "small" | "medium" | "large";
    className?: string;
}

const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({ size = "medium", className = "" }) => {
    const sizeClasses = {
        small: "h-4 w-4 border-2",
        medium: "h-8 w-8 border-3",
        large: "h-12 w-12 border-4",
    };

    return (
        <div className={`flex items-center justify-center ${className}`}>
            <div
                className={`animate-spin rounded-full border-b-transparent border-blue-600 ${sizeClasses[size]}`}
                role="status"
                aria-label="Loading"
            >
                <span className="sr-only">Chargement...</span>
            </div>
        </div>
    );
};

export default LoadingSpinner;
