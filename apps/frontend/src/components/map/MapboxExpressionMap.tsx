"use client";

import React, { useEffect, useRef, useState, useMemo } from "react";
import mapboxgl from "mapbox-gl";
import "mapbox-gl/dist/mapbox-gl.css";
import { Expression } from "@/types";

// You'll need to set this in your environment variables
mapboxgl.accessToken = process.env.NEXT_PUBLIC_MAPBOX_ACCESS_TOKEN || "";

interface MapboxExpressionMapProps {
    expressions: Expression[];
    selectedExpression: Expression | null;
    onSelectExpression: (expression: Expression | null) => void;
    showHeatmap?: boolean;
    showClusters?: boolean;
}

const MapboxExpressionMap: React.FC<MapboxExpressionMapProps> = ({
    expressions,
    selectedExpression,
    onSelectExpression,
    showHeatmap = false,
    showClusters = true,
}) => {
    const mapContainer = useRef<HTMLDivElement>(null);
    const map = useRef<mapboxgl.Map | null>(null);
    const markers = useRef<{ [key: string]: mapboxgl.Marker }>({});
    const [isMapLoaded, setIsMapLoaded] = useState(false);

    // Convert expressions to GeoJSON
    const geojsonData = useMemo(() => {
        const features = expressions
            .filter((exp) => exp.geolocation?.lat && exp.geolocation?.lng)
            .map((exp) => ({
                type: "Feature" as const,
                geometry: {
                    type: "Point" as const,
                    coordinates: [exp.geolocation.lng, exp.geolocation.lat],
                },
                properties: {
                    id: exp.documentId,
                    title: exp.titre,
                    type: exp.type_expression,
                    urgency: exp.urgence,
                    status: exp.statut,
                    emotion: exp.etat_emotionnel,
                    piliers: exp.piliers?.map((p) => p.nom).join(", "),
                },
            }));

        return {
            type: "FeatureCollection" as const,
            features,
        };
    }, [expressions]);

    // Initialize map
    useEffect(() => {
        if (!mapContainer.current || map.current) return;

        map.current = new mapboxgl.Map({
            container: mapContainer.current,
            style: "mapbox://styles/mapbox/light-v11",
            center: [2.2137, 46.2276], // Center of France
            zoom: 5.5,
            pitch: 0,
            bearing: 0,
        });

        // Add navigation controls
        map.current.addControl(new mapboxgl.NavigationControl(), "top-right");

        // Add geolocate control
        map.current.addControl(
            new mapboxgl.GeolocateControl({
                positionOptions: {
                    enableHighAccuracy: true,
                },
                trackUserLocation: true,
                showUserHeading: true,
            }),
            "top-right",
        );

        // Add scale control
        map.current.addControl(
            new mapboxgl.ScaleControl({
                maxWidth: 200,
                unit: "metric",
            }),
            "bottom-left",
        );

        map.current.on("load", () => {
            setIsMapLoaded(true);
        });

        return () => {
            map.current?.remove();
            map.current = null;
        };
    }, []);

    // Add data sources and layers
    useEffect(() => {
        if (!map.current || !isMapLoaded) return;

        const mapInstance = map.current;

        // Add source
        if (!mapInstance.getSource("expressions")) {
            mapInstance.addSource("expressions", {
                type: "geojson",
                data: geojsonData,
                cluster: showClusters,
                clusterMaxZoom: 14,
                clusterRadius: 50,
            });
        } else {
            const source = mapInstance.getSource("expressions") as mapboxgl.GeoJSONSource;
            source.setData(geojsonData);
        }

        // Add cluster layer
        if (showClusters && !mapInstance.getLayer("clusters")) {
            mapInstance.addLayer({
                id: "clusters",
                type: "circle",
                source: "expressions",
                filter: ["has", "point_count"],
                paint: {
                    "circle-color": [
                        "step",
                        ["get", "point_count"],
                        "#51bbd6",
                        10,
                        "#f1f075",
                        30,
                        "#f28cb1",
                    ],
                    "circle-radius": ["step", ["get", "point_count"], 20, 10, 30, 30, 40],
                    "circle-stroke-width": 2,
                    "circle-stroke-color": "#ffffff",
                },
            });

            mapInstance.addLayer({
                id: "cluster-count",
                type: "symbol",
                source: "expressions",
                filter: ["has", "point_count"],
                layout: {
                    "text-field": "{point_count_abbreviated}",
                    "text-font": ["DIN Offc Pro Medium", "Arial Unicode MS Bold"],
                    "text-size": 12,
                },
                paint: {
                    "text-color": "#ffffff",
                },
            });
        }

        // Add unclustered points layer
        if (!mapInstance.getLayer("unclustered-point")) {
            mapInstance.addLayer({
                id: "unclustered-point",
                type: "circle",
                source: "expressions",
                filter: ["!", ["has", "point_count"]],
                paint: {
                    "circle-color": [
                        "match",
                        ["get", "type"],
                        "probleme",
                        "#ef4444",
                        "satisfaction",
                        "#10b981",
                        "idee",
                        "#f59e0b",
                        "question",
                        "#3b82f6",
                        "#6b7280",
                    ],
                    "circle-radius": ["interpolate", ["linear"], ["get", "urgency"], 1, 6, 5, 12],
                    "circle-stroke-width": 2,
                    "circle-stroke-color": "#ffffff",
                    "circle-opacity": 0.8,
                },
            });
        }

        // Add heatmap layer
        if (showHeatmap && !mapInstance.getLayer("expressions-heat")) {
            mapInstance.addLayer({
                id: "expressions-heat",
                type: "heatmap",
                source: "expressions",
                maxzoom: 15,
                paint: {
                    "heatmap-weight": ["interpolate", ["linear"], ["get", "urgency"], 1, 0.2, 5, 1],
                    "heatmap-intensity": ["interpolate", ["linear"], ["zoom"], 0, 1, 15, 3],
                    "heatmap-color": [
                        "interpolate",
                        ["linear"],
                        ["heatmap-density"],
                        0,
                        "rgba(33,102,172,0)",
                        0.2,
                        "rgb(103,169,207)",
                        0.4,
                        "rgb(209,229,240)",
                        0.6,
                        "rgb(253,219,199)",
                        0.8,
                        "rgb(239,138,98)",
                        1,
                        "rgb(178,24,43)",
                    ],
                    "heatmap-radius": ["interpolate", ["linear"], ["zoom"], 0, 2, 15, 20],
                    "heatmap-opacity": 0.6,
                },
            });
        } else if (!showHeatmap && mapInstance.getLayer("expressions-heat")) {
            mapInstance.removeLayer("expressions-heat");
        }

        // Click handlers
        const handleClusterClick = (e: mapboxgl.MapMouseEvent) => {
            const features = mapInstance.queryRenderedFeatures(e.point, {
                layers: ["clusters"],
            });

            if (!features.length) return;

            const clusterId = features[0].properties?.cluster_id;
            const source = mapInstance.getSource("expressions") as mapboxgl.GeoJSONSource;

            source.getClusterExpansionZoom(clusterId, (err, zoom) => {
                if (err) return;

                const coordinates = (features[0].geometry as any).coordinates;
                mapInstance.easeTo({
                    center: coordinates,
                    zoom: zoom || 10,
                });
            });
        };

        const handlePointClick = (e: mapboxgl.MapMouseEvent) => {
            const features = mapInstance.queryRenderedFeatures(e.point, {
                layers: ["unclustered-point"],
            });

            if (!features.length) return;

            const expressionId = features[0].properties?.id;
            const expression = expressions.find((exp) => exp.documentId === expressionId);

            if (expression) {
                onSelectExpression(expression);
            }
        };

        // Cursor changes
        const handleMouseEnter = () => {
            mapInstance.getCanvas().style.cursor = "pointer";
        };

        const handleMouseLeave = () => {
            mapInstance.getCanvas().style.cursor = "";
        };

        // Add event listeners
        mapInstance.on("click", "clusters", handleClusterClick);
        mapInstance.on("click", "unclustered-point", handlePointClick);
        mapInstance.on("mouseenter", "clusters", handleMouseEnter);
        mapInstance.on("mouseleave", "clusters", handleMouseLeave);
        mapInstance.on("mouseenter", "unclustered-point", handleMouseEnter);
        mapInstance.on("mouseleave", "unclustered-point", handleMouseLeave);

        return () => {
            // Cleanup event listeners
            mapInstance.off("click", "clusters", handleClusterClick);
            mapInstance.off("click", "unclustered-point", handlePointClick);
            mapInstance.off("mouseenter", "clusters", handleMouseEnter);
            mapInstance.off("mouseleave", "clusters", handleMouseLeave);
            mapInstance.off("mouseenter", "unclustered-point", handleMouseEnter);
            mapInstance.off("mouseleave", "unclustered-point", handleMouseLeave);
        };
    }, [geojsonData, expressions, showClusters, showHeatmap, isMapLoaded, onSelectExpression]);

    // Handle selected expression
    useEffect(() => {
        if (!map.current || !selectedExpression) return;

        const coords = selectedExpression.geolocation;
        if (!coords?.lat || !coords?.lng) return;

        map.current.flyTo({
            center: [coords.lng, coords.lat],
            zoom: 14,
            duration: 1000,
        });

        // Add a popup
        const popup = new mapboxgl.Popup({ offset: 25 })
            .setLngLat([coords.lng, coords.lat])
            .setHTML(
                `<div class="p-2">
                    <h3 class="font-semibold">${selectedExpression.titre}</h3>
                    <p class="text-sm text-gray-600 mt-1">
                        ${selectedExpression.type_expression} - Urgence: ${selectedExpression.urgence}/5
                    </p>
                </div>`,
            )
            .addTo(map.current);

        return () => {
            popup.remove();
        };
    }, [selectedExpression]);

    return (
        <div className="relative w-full h-full">
            <div ref={mapContainer} className="absolute inset-0" />

            {/* Map type toggle */}
            <div className="absolute top-4 left-4 bg-white rounded-lg shadow-md p-2">
                <div className="flex items-center gap-2">
                    <button
                        onClick={() => {
                            if (map.current) {
                                map.current.setStyle("mapbox://styles/mapbox/streets-v12");
                            }
                        }}
                        className="px-3 py-1 text-sm rounded hover:bg-gray-100"
                    >
                        Plan
                    </button>
                    <button
                        onClick={() => {
                            if (map.current) {
                                map.current.setStyle(
                                    "mapbox://styles/mapbox/satellite-streets-v12",
                                );
                            }
                        }}
                        className="px-3 py-1 text-sm rounded hover:bg-gray-100"
                    >
                        Satellite
                    </button>
                </div>
            </div>
        </div>
    );
};

export default MapboxExpressionMap;
