import { useCallback } from "react";
import { useNotificationStore } from "@/stores/notificationStore";

interface WebSocketMessage {
    type: "notification" | "expression_update" | "user_activity" | "system_status";
    data: any;
    timestamp: string;
}

class RealtimeService {
    private ws: WebSocket | null = null;
    private reconnectAttempts = 0;
    private maxReconnectAttempts = 5;
    private reconnectDelay = 1000;
    private userId: string | null = null;
    private token: string | null = null;

    connect(userId: string, token: string) {
        this.userId = userId;
        this.token = token;
        this.initializeConnection();
    }

    private initializeConnection() {
        try {
            // In a real implementation, this would connect to your WebSocket server
            // For demo purposes, we'll simulate the connection
            console.log("Attempting to connect to WebSocket...");

            // Simulate connection success
            setTimeout(() => {
                this.onConnect();
            }, 1000);
        } catch (error) {
            console.error("WebSocket connection failed:", error);
            this.onError();
        }
    }

    private onConnect() {
        console.log("WebSocket connected");
        this.reconnectAttempts = 0;
        useNotificationStore.getState().setConnectionStatus(true);

        // Send authentication message (simulated)
        this.sendMessage({
            type: "auth",
            data: {
                userId: this.userId,
                token: this.token,
            },
        });
    }

    private onMessage(event: MessageEvent) {
        try {
            const message: WebSocketMessage = JSON.parse(event.data);
            this.handleMessage(message);
        } catch (error) {
            console.error("Error parsing WebSocket message:", error);
        }
    }

    private handleMessage(message: WebSocketMessage) {
        const { addNotification } = useNotificationStore.getState();

        switch (message.type) {
            case "notification":
                addNotification({
                    title: message.data.title,
                    message: message.data.message,
                    type: message.data.type || "info",
                    actionUrl: message.data.actionUrl,
                    actionLabel: message.data.actionLabel,
                    expressionId: message.data.expressionId,
                    userId: message.data.userId,
                });
                break;

            case "expression_update":
                if (message.data.status === "approved") {
                    addNotification({
                        title: "Expression approuvée",
                        message: `Votre expression "${message.data.title}" a été approuvée et est maintenant visible.`,
                        type: "success",
                        actionUrl: `/expressions/${message.data.id}`,
                        actionLabel: "Voir l'expression",
                        expressionId: message.data.id,
                    });
                } else if (message.data.status === "resolved") {
                    addNotification({
                        title: "Expression résolue",
                        message: `Votre expression "${message.data.title}" a été marquée comme résolue.`,
                        type: "success",
                        actionUrl: `/expressions/${message.data.id}`,
                        actionLabel: "Voir la résolution",
                        expressionId: message.data.id,
                    });
                }
                break;

            case "user_activity":
                // Handle user activity updates (like new comments, likes, etc.)
                break;

            case "system_status":
                if (message.data.maintenance) {
                    addNotification({
                        title: "Maintenance programmée",
                        message: message.data.message,
                        type: "warning",
                    });
                }
                break;

            default:
                console.log("Unknown message type:", message.type);
        }
    }

    private onError() {
        console.error("WebSocket error");
        useNotificationStore.getState().setConnectionStatus(false);
        this.attemptReconnect();
    }

    private onClose() {
        console.log("WebSocket connection closed");
        useNotificationStore.getState().setConnectionStatus(false);
        this.attemptReconnect();
    }

    private attemptReconnect() {
        if (this.reconnectAttempts < this.maxReconnectAttempts) {
            this.reconnectAttempts++;
            const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);

            console.log(
                `Attempting to reconnect in ${delay}ms (attempt ${this.reconnectAttempts})`,
            );

            setTimeout(() => {
                this.initializeConnection();
            }, delay);
        } else {
            console.error("Max reconnection attempts reached");
            useNotificationStore.getState().addNotification({
                title: "Connexion perdue",
                message: "Impossible de se reconnecter au serveur. Rechargez la page.",
                type: "error",
            });
        }
    }

    private sendMessage(message: any) {
        if (this.ws && this.ws.readyState === WebSocket.OPEN) {
            this.ws.send(JSON.stringify(message));
        }
    }

    disconnect() {
        if (this.ws) {
            this.ws.close();
            this.ws = null;
        }
        useNotificationStore.getState().setConnectionStatus(false);
    }

    // Public methods for sending messages
    sendExpressionUpdate(expressionId: string, status: string) {
        this.sendMessage({
            type: "expression_update",
            data: { expressionId, status },
        });
    }

    sendUserActivity(activity: string, data: any) {
        this.sendMessage({
            type: "user_activity",
            data: { activity, ...data },
        });
    }
}

export const realtimeService = new RealtimeService();

// React hook for using realtime service
export function useRealtime() {
    // Use useCallback to ensure stable function references
    const connect = useCallback((userId: string, token: string) => {
        realtimeService.connect(userId, token);
    }, []);

    const disconnect = useCallback(() => {
        realtimeService.disconnect();
    }, []);

    const sendExpressionUpdate = useCallback((expressionId: string, status: string) => {
        realtimeService.sendExpressionUpdate(expressionId, status);
    }, []);

    const sendUserActivity = useCallback((activity: string, data: any) => {
        realtimeService.sendUserActivity(activity, data);
    }, []);

    return {
        connect,
        disconnect,
        sendExpressionUpdate,
        sendUserActivity,
    };
}
