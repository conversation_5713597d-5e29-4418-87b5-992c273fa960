// PillarScan Types

export interface User {
    documentId: string;
    email: string;
    username: string;
    confirmed: boolean;
    blocked: boolean;
    createdAt: string;
    updatedAt: string;
    profile?: Profile;
}

export interface Profile {
    documentId: string;
    nom: string;
    telephone?: string;
    date_naissance?: string;
    genre: "M" | "F" | "Autre";
    lieu_residence?: Lieu;
    validateur_assigne?: Validateur;
    role: ProfileRole;
    statut: "actif" | "suspendu" | "inactif";
    preferences: {
        notifications_email: boolean;
        notifications_push: boolean;
        langue: string;
        theme: string;
    };
    date_inscription: string;
    derniere_activite?: string;
    nb_expressions: number;
    score_reputation: number;
    compte_verifie: boolean;
    user: string;
    metadata?: Record<string, any>;
}

export type ProfileRole =
    | "observateur"
    | "contributeur"
    | "contributeur_verifie"
    | "validateur"
    | "validateur_senior"
    | "admin_regional"
    | "admin_national"
    | "super_admin";

export interface Pilier {
    documentId: string;
    code: string;
    nom: string;
    nom_en?: string;
    description: string;
    domaine: "services_publics" | "vie_quotidienne" | "economie_social" | "gouvernance_democratie";
    ordre: number;
    icone: string;
    couleur: string;
    actif: boolean;
    sous_piliers?: SousPilier[];
    expressions?: Expression[];
    validateurs_specialistes?: Validateur[];
    statistiques: {
        nb_expressions_total: number;
        nb_expressions_mois: number;
        taux_resolution: number;
        temps_moyen_resolution: number;
    };
    metadata?: Record<string, any>;
}

export interface SousPilier {
    documentId: string;
    code: string;
    nom: string;
    nom_en?: string;
    description?: string;
    ordre: number;
    actif: boolean;
    pilier: string | Pilier;
    expressions?: Expression[];
    mots_cles: string[];
    statistiques: {
        nb_expressions_total: number;
        nb_expressions_mois: number;
        taux_resolution: number;
    };
    metadata?: Record<string, any>;
}

export interface Lieu {
    documentId: string;
    nom: string;
    type: "adresse" | "zone" | "point_interet" | "commune" | "departement" | "region" | "pays";
    niveau: "pays" | "region" | "departement" | "ville" | "quartier" | "rue";
    coordonnees: {
        lat: number;
        lng: number;
        [key: string]: any;
    };
    adresse_complete?: string;
    code_postal?: string;
    rue?: string;
    ville?: string;
    departement?: string;
    region?: string;
    pays: string;
    parent?: Lieu;
    enfants?: Lieu[];
    expressions?: Expression[];
    residents?: Profile[];
    entites?: Entite[];
    actif: boolean;
    verifie: boolean;
    metadata?: Record<string, any>;
}

export interface Entite {
    documentId: string;
    nom: string;
    type: "personne" | "organisation" | "groupe" | "service";
    sous_type?: string;
    description?: string;
    identifiants: Record<string, any>;
    contacts: Record<string, any>;
    lieu_principal?: Lieu;
    responsable?: Profile;
    expressions?: Expression[];
    verifie: boolean;
    actif: boolean;
    reputation_score: number;
    nb_mentions: number;
    derniere_mention?: string;
    logo?: any;
    site_web?: string;
    reseaux_sociaux: Record<string, any>;
    metadata?: Record<string, any>;
}

export interface Expression {
    documentId: string;
    titre: string;
    contenu: string;
    type_expression: "probleme" | "satisfaction" | "idee" | "question";
    urgence: 1 | 2 | 3 | 4 | 5;
    etat_emotionnel: "colere" | "joie" | "tristesse" | "espoir" | "neutre" | "frustration";
    statut: ExpressionStatus;
    raison_rejet?: string;
    auteur: string | Profile;
    validateur?: string | Validateur;
    date_evenement: string;
    date_creation: string;
    date_soumission?: string;
    date_publication?: string;
    date_resolution?: string;
    lieu: string | Lieu;
    piliers?: Pilier[];
    sous_piliers?: SousPilier[];
    entites?: Entite[];
    medias?: any[]; // This refers to Strapi's media type, which can be complex.
    // For form data, we might use File[] or number[] (IDs)
    geolocation: Record<string, any>;
    score_ia: Record<string, any>;
    analyse_ia: {
        score_confiance: number;
        mots_cles: string[];
        entites_extraites: string[];
        sentiment_score: number;
        categorie_suggeree: string;
        urgence_calculee: number;
    };
    impact: {
        vues: number;
        soutiens: number;
        partages: number;
        commentaires: number;
    };
    actions_prises?: Action[];
    satisfaction_resolution?: number;
    tags: string[];
    source: "web" | "mobile" | "api" | "import";
    langue: string;
    version: number;
    metadata?: Record<string, any>;
}

export type ExpressionStatus =
    | "brouillon"
    | "en_moderation"
    | "publie"
    | "en_traitement"
    | "resolu"
    | "rejete"
    | "archive";

export interface Validateur {
    documentId: string;
    profile: string | Profile;
    perimetres?: Perimetre[];
    specialites?: Pilier[];
    niveau: "junior" | "senior" | "expert" | "formateur";
    quota_jour: number;
    quota_utilise_jour: number;
    actif: boolean;
    date_nomination: string;
    date_derniere_activite?: string;
    profiles_suivis?: Profile[];
    expressions_validees?: Expression[];
    statistiques: {
        nb_validations_total: number;
        nb_validations_mois: number;
        taux_approbation: number;
        temps_moyen_validation: number;
        score_qualite: number;
    };
    formations_completees: string[];
    certifications: string[];
    notes_performance?: string;
    disponibilite: {
        horaires: string;
        jours: string[];
        vacances: boolean;
    };
    metadata?: Record<string, any>;
}

export interface Perimetre {
    documentId: string;
    nom: string;
    type:
        | "global"
        | "national"
        | "regional"
        | "departemental"
        | "local"
        | "thematique"
        | "linguistique";
    description?: string;
    zones_geographiques?: Lieu[];
    piliers_concernes?: Pilier[];
    validateurs?: Validateur[];
    actif: boolean;
    priorite: number;
    coordonnees_geo?: Record<string, any>;
    criteres_inclusion: Record<string, any>;
    metadata?: Record<string, any>;
}

export interface Action {
    documentId: string;
    titre: string;
    description: string;
    type_action:
        | "investigation"
        | "reparation"
        | "amelioration"
        | "communication"
        | "formation"
        | "politique"
        | "autre";
    statut: "planifiee" | "en_cours" | "terminee" | "suspendue" | "annulee";
    priorite: "basse" | "normale" | "haute" | "critique";
    expression: string | Expression;
    responsable?: Profile;
    entite_responsable?: Entite;
    date_creation: string;
    date_debut_prevue?: string;
    date_fin_prevue?: string;
    date_debut_reelle?: string;
    date_fin_reelle?: string;
    budget_estime?: number;
    budget_reel?: number;
    progression: number;
    resultats?: string;
    preuves?: any[];
    impact_mesure: Record<string, any>;
    commentaires_publics?: string;
    visible_public: boolean;
    metadata?: Record<string, any>;
}

// API Response Types
export interface ApiResponse<T extends any[] | any> {
    data: T;
    meta?: {
        pagination?: {
            page: number;
            pageSize: number;
            pageCount: number;
            total: number;
        };
    };
}

export interface ApiError {
    error: {
        status: number;
        name: string;
        message: string;
        details?: Record<string, any>;
    };
}

// Form Types
export interface ExpressionFormData {
    titre: string;
    contenu: string;
    type_expression: Expression["type_expression"];
    urgence: Expression["urgence"];
    etat_emotionnel: Expression["etat_emotionnel"];
    date_evenement: string;
    lieu: string;
    piliers?: string[];
    sous_piliers?: string[];
    entites?: string[];
    medias?: File[] | string[]; // Can be File[] for form or number[] for Strapi IDs
    geolocation?: {
        lat: number;
        lng: number;
    };
    tags?: string[];
}

export interface ProfileFormData {
    nom: string;
    telephone?: string;
    date_naissance?: string;
    genre: Profile["genre"];
    lieu_residence?: string;
    preferences: Profile["preferences"];
}

// Filter Types
export interface ExpressionFilters {
    pilier?: string;
    lieu?: string;
    type_expression?: Expression["type_expression"];
    urgence_min?: number;
    date_debut?: string;
    date_fin?: string;
    statut?: ExpressionStatus;
    page?: number;
    pageSize?: number;
    myExpressions?: string; // Filter to get only current user's expressions
    auteur?: string; // Filter by specific author ID
}

export interface PilierStats {
    total_expressions: number;
    by_status: Record<string, number>;
    by_type: Record<string, number>;
    by_urgency: Record<string, number>;
    resolution_rate: number;
    avg_resolution_time: number;
    monthly_trend: Record<string, number>;
    sub_pillars: Record<string, number>;
}

export interface ProfileStats {
    total_expressions: number;
    by_status: Record<string, number>;
    by_type: Record<string, number>;
    by_urgency: Record<string, number>;
    monthly_activity: Record<string, number>;
    resolution_rate: number;
}

export interface StrapiFile {
    documentId: string;
    name: string;
    alternativeText?: string | null;
    caption?: string | null;
    width?: number | null;
    height?: number | null;
    formats?: Record<
        string,
        {
            ext: string;
            url: string;
            hash: string;
            mime: string;
            name: string;
            path?: string | null;
            size: number;
            width: number;
            height: number;
        }
    > | null;
    hash: string;
    ext: string;
    mime: string;
    size: number;
    url: string;
    previewUrl?: string | null;
    provider: string;
    provider_metadata?: Record<string, any> | null;
    createdAt: string;
    updatedAt: string;
}

export interface AuthResponse {
    jwt: string;
    user: User;
}

export interface ForgotPasswordResponse {
    ok: boolean;
    message?: string; // Or more specific based on your API
}

export interface ResetPasswordResponse {
    ok: boolean;
    message?: string; // Or more specific based on your API
    user?: User; // Strapi might return user details
}

// Server notification type from API
export interface ServerNotification {
    documentId: string; // Strapi document ID for deletion
    id: string;
    title: string;
    message: string;
    type: "info" | "success" | "warning" | "error";
    createdAt: string;
    read: boolean;
    category: "expression" | "moderation" | "system" | "social" | "update";
    metadata?: {
        expressionId?: string;
        userId?: string;
        actionUrl?: string;
        actionLabel?: string;
        [key: string]: any;
    };
}
