import { NextResponse } from "next/server";
import type { NextRequest } from "next/server";
import { AUTH_COOKIE_NAME, getDecodedToken } from "./lib/auth/cookies";

// Define public routes that don't require authentication
const publicRoutes = [
    "/",
    "/auth/login",
    "/auth/register",
    "/auth/forgot-password",
    "/api/auth",
    "/piliers", // Public pillars page
    "/expressions", // Public expressions list (excluding /new)
    "/carte", // Public map page
];

// Define protected routes that require authentication
const protectedRoutes = [
    "/dashboard",
    "/profile",
    "/expressions/new",
    "/moderation",
    "/notifications",
];

// Define admin-only routes
const adminRoutes = ["/analytics", "/moderation"];

// Define role requirements for specific routes
const roleBasedRoutes: Record<string, string[]> = {
    "/analytics": ["super_admin", "validateur"],
    "/moderation": ["super_admin", "validateur"],
};

function isPublicRoute(pathname: string): boolean {
    // Special handling for expressions routes
    if (pathname.startsWith("/expressions")) {
        // /expressions/new is protected, but other expression routes are public
        return pathname !== "/expressions/new";
    }

    return publicRoutes.some((route) => pathname === route || pathname.startsWith(route + "/"));
}

function isProtectedRoute(pathname: string): boolean {
    return protectedRoutes.some((route) => pathname === route || pathname.startsWith(route + "/"));
}

function requiresRole(pathname: string): string[] | null {
    for (const [route, roles] of Object.entries(roleBasedRoutes)) {
        if (pathname === route || pathname.startsWith(route + "/")) {
            return roles;
        }
    }
    return null;
}

export async function middleware(request: NextRequest) {
    const { pathname } = request.nextUrl;

    // Allow public routes
    if (isPublicRoute(pathname)) {
        return NextResponse.next();
    }

    // Static assets and API routes (except auth) should pass through
    if (
        pathname.startsWith("/_next") ||
        pathname.startsWith("/api/") ||
        pathname.includes(".") // Files with extensions
    ) {
        return NextResponse.next();
    }

    // Check authentication
    const token = await getDecodedToken();

    if (!token) {
        // Not authenticated - redirect to login
        const loginUrl = new URL("/auth/login", request.url);
        loginUrl.searchParams.set("from", pathname);

        // Remove invalid cookie if it exists
        const response = NextResponse.redirect(loginUrl);
        if (request.cookies.has(AUTH_COOKIE_NAME)) {
            response.cookies.delete(AUTH_COOKIE_NAME);
        }
        return response;
    }

    // Check if token is expired
    const tokenExp = token.exp * 1000;
    const now = Date.now();

    if (tokenExp < now) {
        // Token is expired - redirect to login
        const loginUrl = new URL("/auth/login", request.url);
        loginUrl.searchParams.set("from", pathname);

        // Remove expired cookie
        const response = NextResponse.redirect(loginUrl);
        response.cookies.delete(AUTH_COOKIE_NAME);
        return response;
    }

    // For protected routes, authentication is enough
    if (isProtectedRoute(pathname)) {
        // Check if token is about to expire (less than 1 hour remaining)
        const oneHour = 60 * 60 * 1000;

        if (tokenExp - now < oneHour) {
            // Token is about to expire, add a header to trigger refresh
            const response = NextResponse.next();
            response.headers.set("X-Token-Refresh-Needed", "true");
            return response;
        }

        return NextResponse.next();
    }

    // For role-based routes, we need to check roles
    // Note: In a production app, you might want to include roles in the JWT token
    // or fetch user profile here to check roles
    const requiredRoles = requiresRole(pathname);
    if (requiredRoles) {
        // For now, we'll allow access but the page component will do the role check
        // In production, you'd want to check roles here
        return NextResponse.next();
    }

    // For all other authenticated routes
    return NextResponse.next();
}

export const config = {
    matcher: [
        /*
         * Match all request paths except for the ones starting with:
         * - _next/static (static files)
         * - _next/image (image optimization files)
         * - favicon.ico (favicon file)
         * - public files
         * - api routes (handled separately)
         */
        "/((?!_next/static|_next/image|favicon.ico|.*\\..*|api/).*)",
    ],
};
