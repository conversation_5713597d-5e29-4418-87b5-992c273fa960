"use client";

import React, { useState, useEffect, useCallback, useMemo } from "react";
import dynamic from "next/dynamic";
import { Expression, Pilier, ExpressionStatus } from "@/types";
import {
    MapPinIcon,
    FunnelIcon,
    ExclamationTriangleIcon,
    CheckCircleIcon,
    LightBulbIcon,
    QuestionMarkCircleIcon,
    XMarkIcon,
    ChartBarIcon,
    FireIcon,
    ClockIcon,
    GlobeAltIcon,
} from "@heroicons/react/24/outline";
import LoadingSpinner from "@/components/ui/LoadingSpinner";
import { Card } from "@/components/ui/Card";
import Select from "@/components/ui/Select";
import Logo from "@/components/ui/Logo";

// Dynamically import map component to avoid SSR issues
const ExpressionMapComponent = dynamic(() => import("@/components/map/ExpressionMap"), {
    loading: () => <LoadingSpinner size="large" />,
    ssr: false,
});

interface MapFilters {
    pilier?: string;
    type_expression?: "probleme" | "satisfaction" | "idee" | "question";
    urgence_min?: number;
    statut?: ExpressionStatus;
}

interface MapClientProps {
    piliers: Pilier[];
}

interface PaginationState {
    page: number;
    pageSize: number;
    total: number;
    hasMore: boolean;
}

const MapClient: React.FC<MapClientProps> = ({ piliers }) => {
    const [expressions, setExpressions] = useState<Expression[]>([]);
    const [loading, setLoading] = useState(true);
    const [loadingMore, setLoadingMore] = useState(false);
    const [filters, setFilters] = useState<MapFilters>({});
    const [selectedExpression, setSelectedExpression] = useState<Expression | null>(null);
    const [showFilters, setShowFilters] = useState(false);
    const [pagination, setPagination] = useState<PaginationState>({
        page: 1,
        pageSize: 100, // Load 100 expressions at a time for better performance
        total: 0,
        hasMore: true,
    });

    // Fetch expressions based on filters
    const fetchExpressions = useCallback(
        async (page: number = 1, append: boolean = false) => {
            try {
                if (page === 1) {
                    setLoading(true);
                } else {
                    setLoadingMore(true);
                }

                const { fetchPublicExpressionsAction } = await import("@/lib/actions");
                const result = await fetchPublicExpressionsAction({
                    ...filters,
                    page,
                    pageSize: pagination.pageSize,
                });

                if (result.success && result.data) {
                    if (append) {
                        setExpressions((prev) => [...prev, ...(result.data || [])]);
                    } else {
                        setExpressions(result.data || []);
                    }

                    // Update pagination state
                    if (result.meta?.pagination) {
                        const { total, pageCount } = result.meta.pagination;
                        setPagination((prev) => ({
                            ...prev,
                            page,
                            total,
                            hasMore: page < pageCount,
                        }));
                    }
                } else {
                    console.error("Failed to fetch expressions:", result.error);
                    if (!append) {
                        setExpressions([]);
                    }
                    setPagination((prev) => ({ ...prev, hasMore: false }));
                }
            } catch (error) {
                console.error("Error fetching expressions:", error);
                if (!append) {
                    setExpressions([]);
                }
                setPagination((prev) => ({ ...prev, hasMore: false }));
            } finally {
                setLoading(false);
                setLoadingMore(false);
            }
        },
        [filters, pagination.pageSize],
    );

    // Reset pagination when filters change
    useEffect(() => {
        setPagination((prev) => ({
            ...prev,
            page: 1,
            total: 0,
            hasMore: true,
        }));
        fetchExpressions(1, false);
    }, [filters]); // Explicitly depend on filters only

    // Load more expressions
    const loadMore = useCallback(() => {
        if (!loadingMore && pagination.hasMore) {
            fetchExpressions(pagination.page + 1, true);
        }
    }, [fetchExpressions, loadingMore, pagination.hasMore, pagination.page]);

    const handleFilterChange = (key: keyof MapFilters, value: any) => {
        setFilters((prev) => ({
            ...prev,
            [key]: value || undefined,
        }));
    };

    const getExpressionIcon = (type: string) => {
        switch (type) {
            case "probleme":
                return <ExclamationTriangleIcon className="h-5 w-5 text-red-500" />;
            case "satisfaction":
                return <CheckCircleIcon className="h-5 w-5 text-green-500" />;
            case "idee":
                return <LightBulbIcon className="h-5 w-5 text-yellow-500" />;
            case "question":
                return <QuestionMarkCircleIcon className="h-5 w-5 text-blue-500" />;
            default:
                return <MapPinIcon className="h-5 w-5 text-gray-500" />;
        }
    };

    const getUrgencyColor = (urgency: number) => {
        if (urgency >= 4) return "text-red-600";
        if (urgency >= 3) return "text-orange-600";
        return "text-green-600";
    };

    // Calculate statistics
    const statistics = useMemo(() => {
        const stats = {
            total: expressions.length,
            byType: {
                probleme: 0,
                satisfaction: 0,
                idee: 0,
                question: 0,
            },
            byUrgency: {
                high: 0, // 4-5
                medium: 0, // 3
                low: 0, // 1-2
            },
            byStatus: {
                publie: 0,
                en_traitement: 0,
                resolu: 0,
            },
            mostAffectedPillar: null as { name: string; count: number } | null,
        };

        const pillarCounts = new Map<string, number>();

        expressions.forEach((exp) => {
            // By type
            if (exp.type_expression in stats.byType) {
                stats.byType[exp.type_expression as keyof typeof stats.byType]++;
            }

            // By urgency
            if (exp.urgence >= 4) stats.byUrgency.high++;
            else if (exp.urgence === 3) stats.byUrgency.medium++;
            else stats.byUrgency.low++;

            // By status
            if (exp.statut === "publie") stats.byStatus.publie++;
            else if (exp.statut === "en_traitement") stats.byStatus.en_traitement++;
            else if (exp.statut === "resolu") stats.byStatus.resolu++;

            // Count pillars
            exp.piliers?.forEach((pillar) => {
                const count = pillarCounts.get(pillar.nom) || 0;
                pillarCounts.set(pillar.nom, count + 1);
            });
        });

        // Find most affected pillar
        let maxCount = 0;
        pillarCounts.forEach((count, name) => {
            if (count > maxCount) {
                maxCount = count;
                stats.mostAffectedPillar = { name, count };
            }
        });

        return stats;
    }, [expressions]);

    return (
        <div className="flex flex-col h-[calc(100vh-4rem)]">
            {/* Header with filters */}
            <div className="bg-white border-b px-4 py-3">
                <div className="max-w-7xl mx-auto">
                    <div className="flex items-center justify-between mb-4">
                        <Logo size="sm" />
                        <button
                            onClick={() => setShowFilters(!showFilters)}
                            className="flex items-center gap-2 px-3 md:px-4 py-2 bg-white border rounded-lg hover:bg-gray-50"
                        >
                            <FunnelIcon className="h-5 w-5" />
                            <span className="hidden sm:inline">Filtres</span>
                        </button>
                    </div>
                    <div>
                        <h1 className="text-xl md:text-2xl font-bold text-gray-900">
                            Carte des expressions citoyennes
                        </h1>
                        <p className="text-sm text-gray-600 mt-1">
                            {expressions.length} sur {pagination.total || expressions.length}{" "}
                            expressions chargées
                        </p>
                    </div>

                    {/* Filters */}
                    {showFilters && (
                        <div className="mt-4 grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-4">
                            <Select
                                label="Pilier"
                                value={filters.pilier || ""}
                                onChange={(value) => handleFilterChange("pilier", value)}
                                options={[
                                    { value: "", label: "Tous les piliers" },
                                    ...piliers.map((p) => ({
                                        value: p.documentId,
                                        label: p.nom,
                                    })),
                                ]}
                            />

                            <Select
                                label="Type d'expression"
                                value={filters.type_expression || ""}
                                onChange={(value) =>
                                    handleFilterChange(
                                        "type_expression",
                                        value as MapFilters["type_expression"],
                                    )
                                }
                                options={[
                                    { value: "", label: "Tous les types" },
                                    { value: "probleme", label: "Problème" },
                                    { value: "satisfaction", label: "Satisfaction" },
                                    { value: "idee", label: "Idée" },
                                    { value: "question", label: "Question" },
                                ]}
                            />

                            <Select
                                label="Urgence minimale"
                                value={filters.urgence_min?.toString() || ""}
                                onChange={(value) =>
                                    handleFilterChange(
                                        "urgence_min",
                                        value ? parseInt(value) : undefined,
                                    )
                                }
                                options={[
                                    { value: "", label: "Toutes les urgences" },
                                    { value: "5", label: "Très urgent (5)" },
                                    { value: "4", label: "Urgent (4+)" },
                                    { value: "3", label: "Modéré (3+)" },
                                    { value: "2", label: "Faible (2+)" },
                                ]}
                            />

                            <Select
                                label="Statut"
                                value={filters.statut || ""}
                                onChange={(value) =>
                                    handleFilterChange("statut", value as MapFilters["statut"])
                                }
                                options={[
                                    { value: "", label: "Tous les statuts" },
                                    { value: "publie", label: "Publié" },
                                    { value: "en_traitement", label: "En traitement" },
                                    { value: "resolu", label: "Résolu" },
                                ]}
                            />
                        </div>
                    )}
                </div>
            </div>

            {/* Statistics Cards */}
            <div className="bg-gray-50 border-b px-4 py-4 overflow-x-auto">
                <div className="max-w-7xl mx-auto">
                    <div className="grid grid-cols-2 lg:grid-cols-4 gap-2 md:gap-4">
                        {/* Total Expressions */}
                        <Card className="p-3 md:p-4 bg-white">
                            <div className="flex items-center justify-between">
                                <div>
                                    <p className="text-xs md:text-sm font-medium text-gray-600">
                                        Total des expressions
                                    </p>
                                    <p className="text-xl md:text-2xl font-bold text-gray-900 mt-1">
                                        {statistics.total}
                                    </p>
                                </div>
                                <ChartBarIcon className="h-6 md:h-8 w-6 md:w-8 text-blue-500" />
                            </div>
                        </Card>

                        {/* Urgent Cases */}
                        <Card className="p-3 md:p-4 bg-white">
                            <div className="flex items-center justify-between">
                                <div>
                                    <p className="text-xs md:text-sm font-medium text-gray-600">
                                        Cas urgents
                                    </p>
                                    <p className="text-xl md:text-2xl font-bold text-red-600 mt-1">
                                        {statistics.byUrgency.high}
                                    </p>
                                    <p className="text-xs text-gray-500 mt-1">Urgence 4-5</p>
                                </div>
                                <FireIcon className="h-6 md:h-8 w-6 md:w-8 text-red-500" />
                            </div>
                        </Card>

                        {/* In Treatment */}
                        <Card className="p-3 md:p-4 bg-white">
                            <div className="flex items-center justify-between">
                                <div>
                                    <p className="text-xs md:text-sm font-medium text-gray-600">
                                        En traitement
                                    </p>
                                    <p className="text-xl md:text-2xl font-bold text-orange-600 mt-1">
                                        {statistics.byStatus.en_traitement}
                                    </p>
                                    <p className="text-xs text-gray-500 mt-1">
                                        {statistics.byStatus.resolu} résolus
                                    </p>
                                </div>
                                <ClockIcon className="h-6 md:h-8 w-6 md:w-8 text-orange-500" />
                            </div>
                        </Card>

                        {/* Most Affected Pillar */}
                        <Card className="p-3 md:p-4 bg-white">
                            <div className="flex items-center justify-between">
                                <div className="flex-1">
                                    <p className="text-xs md:text-sm font-medium text-gray-600">
                                        Pilier le plus touché
                                    </p>
                                    <p className="text-sm md:text-lg font-bold text-gray-900 mt-1 truncate">
                                        {statistics.mostAffectedPillar?.name || "Aucun"}
                                    </p>
                                    <p className="text-xs text-gray-500 mt-1">
                                        {statistics.mostAffectedPillar?.count || 0} expr.
                                    </p>
                                </div>
                                <GlobeAltIcon className="h-6 md:h-8 w-6 md:w-8 text-green-500 flex-shrink-0" />
                            </div>
                        </Card>
                    </div>

                    {/* Expression Type Distribution */}
                    <div className="mt-4 grid grid-cols-2 md:grid-cols-4 gap-2">
                        <div className="bg-white rounded-lg px-3 py-2 border border-gray-200">
                            <div className="flex items-center justify-between">
                                <span className="text-sm text-gray-600">Problèmes</span>
                                <span className="text-sm font-bold text-red-600">
                                    {statistics.byType.probleme}
                                </span>
                            </div>
                        </div>
                        <div className="bg-white rounded-lg px-3 py-2 border border-gray-200">
                            <div className="flex items-center justify-between">
                                <span className="text-sm text-gray-600">Satisfactions</span>
                                <span className="text-sm font-bold text-green-600">
                                    {statistics.byType.satisfaction}
                                </span>
                            </div>
                        </div>
                        <div className="bg-white rounded-lg px-3 py-2 border border-gray-200">
                            <div className="flex items-center justify-between">
                                <span className="text-sm text-gray-600">Idées</span>
                                <span className="text-sm font-bold text-yellow-600">
                                    {statistics.byType.idee}
                                </span>
                            </div>
                        </div>
                        <div className="bg-white rounded-lg px-3 py-2 border border-gray-200">
                            <div className="flex items-center justify-between">
                                <span className="text-sm text-gray-600">Questions</span>
                                <span className="text-sm font-bold text-blue-600">
                                    {statistics.byType.question}
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            {/* Map and sidebar */}
            <div className="flex-1 flex">
                {/* Map */}
                <div className="flex-1 relative">
                    {loading ? (
                        <div className="absolute inset-0 flex items-center justify-center bg-gray-50">
                            <LoadingSpinner size="large" />
                        </div>
                    ) : (
                        <ExpressionMapComponent
                            expressions={expressions}
                            selectedExpression={selectedExpression}
                            onSelectExpression={setSelectedExpression}
                        />
                    )}

                    {/* Load More Button */}
                    {pagination.hasMore && !loading && (
                        <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 z-10">
                            <button
                                onClick={loadMore}
                                disabled={loadingMore}
                                className="px-6 py-2 bg-white text-blue-600 border-2 border-blue-600 rounded-full shadow-lg hover:bg-blue-50 disabled:opacity-50 flex items-center gap-2"
                            >
                                {loadingMore ? (
                                    <>
                                        <LoadingSpinner size="small" />
                                        <span>Chargement...</span>
                                    </>
                                ) : (
                                    <>
                                        <span>Charger plus</span>
                                        <span className="text-sm">
                                            ({pagination.total - expressions.length} restantes)
                                        </span>
                                    </>
                                )}
                            </button>
                        </div>
                    )}
                </div>

                {/* Sidebar with selected expression details */}
                {selectedExpression && (
                    <div className="w-full md:w-96 absolute md:relative inset-0 md:inset-auto bg-white border-l overflow-y-auto z-20 md:z-auto">
                        <div className="p-4">
                            <div className="flex items-start justify-between mb-4">
                                <h3 className="text-lg font-semibold text-gray-900">
                                    Détails de l'expression
                                </h3>
                                <button
                                    onClick={() => setSelectedExpression(null)}
                                    className="text-gray-400 hover:text-gray-600"
                                >
                                    <XMarkIcon className="h-5 w-5" />
                                </button>
                            </div>

                            <Card>
                                <div className="space-y-4">
                                    {/* Type and urgency */}
                                    <div className="flex items-center justify-between">
                                        <div className="flex items-center gap-2">
                                            {getExpressionIcon(selectedExpression.type_expression)}
                                            <span className="text-sm font-medium capitalize">
                                                {selectedExpression.type_expression}
                                            </span>
                                        </div>
                                        <span
                                            className={`text-sm font-bold ${getUrgencyColor(
                                                selectedExpression.urgence,
                                            )}`}
                                        >
                                            Urgence: {selectedExpression.urgence}/5
                                        </span>
                                    </div>

                                    {/* Title */}
                                    <h4 className="font-semibold text-gray-900">
                                        {selectedExpression.titre}
                                    </h4>

                                    {/* Content */}
                                    <p className="text-sm text-gray-600 line-clamp-4">
                                        {selectedExpression.contenu}
                                    </p>

                                    {/* Location */}
                                    {selectedExpression.lieu && (
                                        <div className="flex items-start gap-2">
                                            <MapPinIcon className="h-5 w-5 text-gray-400 flex-shrink-0" />
                                            <div className="text-sm text-gray-600">
                                                <p className="font-medium">
                                                    {typeof selectedExpression.lieu === "string"
                                                        ? selectedExpression.lieu
                                                        : selectedExpression.lieu.nom}
                                                </p>
                                                {typeof selectedExpression.lieu !== "string" &&
                                                    selectedExpression.lieu.adresse_complete && (
                                                        <p>
                                                            {
                                                                selectedExpression.lieu
                                                                    .adresse_complete
                                                            }
                                                        </p>
                                                    )}
                                            </div>
                                        </div>
                                    )}

                                    {/* Pillars */}
                                    {selectedExpression.piliers &&
                                        selectedExpression.piliers.length > 0 && (
                                            <div>
                                                <p className="text-sm font-medium text-gray-700 mb-2">
                                                    Piliers concernés
                                                </p>
                                                <div className="flex flex-wrap gap-2">
                                                    {selectedExpression.piliers.map((pilier) => (
                                                        <span
                                                            key={pilier.documentId}
                                                            className="px-2 py-1 text-xs rounded-full"
                                                            style={{
                                                                backgroundColor: `${pilier.couleur}20`,
                                                                color: pilier.couleur,
                                                            }}
                                                        >
                                                            {pilier.nom}
                                                        </span>
                                                    ))}
                                                </div>
                                            </div>
                                        )}

                                    {/* Actions */}
                                    <div className="pt-4 border-t">
                                        <a
                                            href={`/expressions/${selectedExpression.documentId}`}
                                            className="block w-full text-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition"
                                        >
                                            Voir les détails
                                        </a>
                                    </div>
                                </div>
                            </Card>
                        </div>
                    </div>
                )}
            </div>
        </div>
    );
};

export default MapClient;
