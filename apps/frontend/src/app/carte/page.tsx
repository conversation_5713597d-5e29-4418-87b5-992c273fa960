import { Metadata } from "next";
import MapClient from "./map-client";
import { fetchPiliers } from "@/lib/server-api";
import { Pilier } from "@/types";
import Layout from "@/components/layout/Layout";

export const metadata: Metadata = {
    title: "Carte | PillarScan",
    description: "Explorez les expressions citoyennes sur la carte de France",
};

export default async function CartePage() {
    // Fetch piliers on the server
    let piliers: Pilier[] = [];
    try {
        piliers = await fetchPiliers();
    } catch (error) {
        console.error("Failed to fetch piliers:", error);
    }

    return (
        <Layout showHeader={false} showFooter={false}>
            <MapClient piliers={piliers} />
        </Layout>
    );
}
