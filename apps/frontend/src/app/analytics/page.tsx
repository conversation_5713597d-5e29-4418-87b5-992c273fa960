import { redirect } from "next/navigation";
import { getCurrentUser } from "@/lib/auth/server";
import { fetchAnalytics } from "@/lib/server-api";
import { AnalyticsClient } from "./analytics-client";

export default async function AnalyticsPage() {
    const user = await getCurrentUser();

    if (!user) {
        redirect("/auth/login");
    }

    // Check if user has analytics access
    if (user.profile?.role !== "super_admin" && user.profile?.role !== "validateur") {
        redirect("/dashboard");
    }

    // Fetch analytics data from the server
    let analyticsData = null;
    try {
        analyticsData = await fetchAnalytics("month");
    } catch (error) {
        console.error("Error fetching analytics:", error);
    }

    return <AnalyticsClient user={user} initialData={analyticsData} />;
}
