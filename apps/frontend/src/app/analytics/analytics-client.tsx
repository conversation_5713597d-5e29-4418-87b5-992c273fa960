"use client";

import React, { useState, useTransition } from "react";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle, CardDescription } from "@/components/ui/Card";
import Button from "@/components/ui/Button";
import Badge from "@/components/ui/Badge";
import { getAnalyticsAction } from "./actions";
import {
    ChartBarIcon,
    ArrowTrendingUpIcon,
    ArrowTrendingDownIcon,
    MapPinIcon,
    ClockIcon,
    UserGroupIcon,
    CheckCircleIcon,
    ExclamationTriangleIcon,
    DocumentChartBarIcon,
    CalendarIcon,
    FunnelIcon,
    ArrowDownTrayIcon,
} from "@heroicons/react/24/outline";

// Mock data for analytics
const mockData = {
    overview: {
        totalExpressions: 24567,
        expressionsThisMonth: 3421,
        activeUsers: 12847,
        resolutionRate: 73.5,
        avgResolutionTime: 4.2,
        topCities: ["Paris", "Lyon", "Marseille", "Toulouse", "Nice"],
    },
    trends: {
        expressionsGrowth: 23.4,
        usersGrowth: 18.7,
        resolutionGrowth: 12.3,
        satisfactionGrowth: 8.9,
    },
    pillarStats: [
        { name: "Transport", count: 4523, percentage: 18.4, trend: 12.3, color: "#9b59b6" },
        { name: "Santé", count: 3967, percentage: 16.1, trend: -3.2, color: "#e74c3c" },
        { name: "Environnement", count: 3154, percentage: 12.8, trend: 28.4, color: "#27ae60" },
        { name: "Sécurité", count: 2876, percentage: 11.7, trend: 15.6, color: "#e67e22" },
        { name: "Logement", count: 2543, percentage: 10.3, trend: 5.8, color: "#1abc9c" },
        { name: "Éducation", count: 2234, percentage: 9.1, trend: -1.4, color: "#3498db" },
        { name: "Emploi", count: 1987, percentage: 8.1, trend: 22.1, color: "#f39c12" },
        { name: "Justice", count: 1654, percentage: 6.7, trend: 7.3, color: "#34495e" },
        { name: "Culture", count: 987, percentage: 4.0, trend: -8.2, color: "#ffe66d" },
        { name: "Démocratie", count: 456, percentage: 1.9, trend: 34.5, color: "#4ecdc4" },
        { name: "Vie sociale", count: 123, percentage: 0.5, trend: 45.2, color: "#ff6b6b" },
        { name: "Pouvoir d'achat", count: 87, percentage: 0.4, trend: 67.8, color: "#95a5a6" },
    ],
    timeData: [
        { month: "Janvier", expressions: 1876, resolved: 1342 },
        { month: "Février", expressions: 2134, resolved: 1567 },
        { month: "Mars", expressions: 2456, resolved: 1823 },
        { month: "Avril", expressions: 2789, resolved: 2134 },
        { month: "Mai", expressions: 3123, resolved: 2456 },
        { month: "Juin", expressions: 3421, resolved: 2634 },
    ],
    geographicData: [
        { region: "Île-de-France", expressions: 8765, population: 12262544, rate: 0.71 },
        { region: "Auvergne-Rhône-Alpes", expressions: 3456, population: 8026685, rate: 0.43 },
        { region: "Nouvelle-Aquitaine", expressions: 2876, population: 5956978, rate: 0.48 },
        { region: "Occitanie", expressions: 2543, population: 5924858, rate: 0.43 },
        { region: "Hauts-de-France", expressions: 2234, population: 5962662, rate: 0.37 },
    ],
};

import { User } from "@/types";
import Layout from "@/components/layout/Layout";

interface AnalyticsClientProps {
    user: User;
    initialData?: any;
}

export function AnalyticsClient({ user, initialData }: AnalyticsClientProps) {
    const [timeFilter, setTimeFilter] = useState<"week" | "month" | "year">("month");
    const [selectedPillar, setSelectedPillar] = useState<string | null>(null);
    const [analyticsData, setAnalyticsData] = useState(initialData || mockData);
    const [isPending, startTransition] = useTransition();

    // Handle time filter change with server action
    const handleTimeFilterChange = async (newFilter: "week" | "month" | "year") => {
        setTimeFilter(newFilter);

        if (!initialData) return; // Use mock data if no initial data

        startTransition(async () => {
            const result = await getAnalyticsAction(newFilter);
            if (result.success && result.data) {
                setAnalyticsData(result.data);
            }
        });
    };

    const exportData = () => {
        const data = JSON.stringify(analyticsData, null, 2);
        const blob = new Blob([data], { type: "application/json" });
        const url = URL.createObjectURL(blob);
        const a = document.createElement("a");
        a.href = url;
        a.download = `pillarscan-analytics-${new Date().toISOString().split("T")[0]}.json`;
        a.click();
        URL.revokeObjectURL(url);
    };

    return (
        <Layout showHeader={true} showFooter={false}>
            {/* Header */}
            <section className="civic-gradient text-white py-12">
                <div className="civic-container">
                    <div className="flex items-center justify-between">
                        <div>
                            <Badge
                                variant="primary"
                                size="lg"
                                className="mb-4 bg-white/20 text-white border-white/30"
                            >
                                Analytics & Reporting
                            </Badge>
                            <h1 className="text-4xl font-bold mb-4 font-marianne">
                                Tableau de bord analytique
                            </h1>
                            <p className="text-xl opacity-90 max-w-2xl">
                                Analysez l'impact des expressions citoyennes et mesurez l'évolution
                                de la démocratie participative française.
                            </p>
                        </div>
                        <div className="flex items-center gap-4">
                            <Button
                                variant="secondary"
                                icon={<ArrowDownTrayIcon className="h-4 w-4" />}
                                onClick={exportData}
                            >
                                Exporter
                            </Button>
                            <Button
                                variant="outline"
                                className="border-white text-white hover:bg-white hover:text-primary"
                                icon={<DocumentChartBarIcon className="h-4 w-4" />}
                            >
                                Rapport mensuel
                            </Button>
                        </div>
                    </div>
                </div>
            </section>

            <div className="civic-container py-8">
                {/* Overview Cards */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                    <Card className="p-6">
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm text-muted-foreground">Expressions totales</p>
                                <p className="text-3xl font-bold text-foreground">
                                    {(
                                        analyticsData.overview?.totalExpressions || 0
                                    ).toLocaleString()}
                                </p>
                                <div className="flex items-center mt-2">
                                    <ArrowTrendingUpIcon className="h-4 w-4 text-success mr-1" />
                                    <span className="text-sm text-success">
                                        {isPending ? "..." : "+23%"}
                                    </span>
                                </div>
                            </div>
                            <div className="w-12 h-12 civic-gradient rounded-xl flex items-center justify-center">
                                <ChartBarIcon className="h-6 w-6 text-white" />
                            </div>
                        </div>
                    </Card>

                    <Card className="p-6">
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm text-muted-foreground">Utilisateurs actifs</p>
                                <p className="text-3xl font-bold text-foreground">
                                    {(analyticsData.overview?.activeUsers || 0).toLocaleString()}
                                </p>
                                <div className="flex items-center mt-2">
                                    <ArrowTrendingUpIcon className="h-4 w-4 text-success mr-1" />
                                    <span className="text-sm text-success">
                                        {isPending ? "..." : "+18%"}
                                    </span>
                                </div>
                            </div>
                            <div className="w-12 h-12 bg-info/10 rounded-xl flex items-center justify-center">
                                <UserGroupIcon className="h-6 w-6 text-info" />
                            </div>
                        </div>
                    </Card>

                    <Card className="p-6">
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm text-muted-foreground">Taux de résolution</p>
                                <p className="text-3xl font-bold text-foreground">
                                    {mockData.overview.resolutionRate}%
                                </p>
                                <div className="flex items-center mt-2">
                                    <ArrowTrendingUpIcon className="h-4 w-4 text-success mr-1" />
                                    <span className="text-sm text-success">
                                        +{mockData.trends.resolutionGrowth}%
                                    </span>
                                </div>
                            </div>
                            <div className="w-12 h-12 bg-success/10 rounded-xl flex items-center justify-center">
                                <CheckCircleIcon className="h-6 w-6 text-success" />
                            </div>
                        </div>
                    </Card>

                    <Card className="p-6">
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm text-muted-foreground">
                                    Temps moyen résolution
                                </p>
                                <p className="text-3xl font-bold text-foreground">
                                    {mockData.overview.avgResolutionTime} jours
                                </p>
                                <div className="flex items-center mt-2">
                                    <ArrowTrendingDownIcon className="h-4 w-4 text-success mr-1" />
                                    <span className="text-sm text-success">-15% plus rapide</span>
                                </div>
                            </div>
                            <div className="w-12 h-12 bg-warning/10 rounded-xl flex items-center justify-center">
                                <ClockIcon className="h-6 w-6 text-warning" />
                            </div>
                        </div>
                    </Card>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
                    {/* Pillar Analysis */}
                    <Card className="p-6">
                        <CardHeader className="px-0 pt-0">
                            <CardTitle>Analyse par piliers</CardTitle>
                            <CardDescription>
                                Distribution des expressions par domaine de société
                            </CardDescription>
                        </CardHeader>
                        <CardContent className="px-0">
                            <div className="space-y-4">
                                {mockData.pillarStats.slice(0, 6).map((pillar, index) => (
                                    <div
                                        key={index}
                                        className="flex items-center justify-between p-3 rounded-lg hover:bg-secondary/50 transition-colors cursor-pointer"
                                        onClick={() =>
                                            setSelectedPillar(
                                                selectedPillar === pillar.name ? null : pillar.name,
                                            )
                                        }
                                    >
                                        <div className="flex items-center gap-3 flex-1">
                                            <div
                                                className="w-4 h-4 rounded-full"
                                                style={{ backgroundColor: pillar.color }}
                                            />
                                            <div className="flex-1">
                                                <div className="flex items-center justify-between">
                                                    <span className="font-medium text-foreground">
                                                        {pillar.name}
                                                    </span>
                                                    <span className="text-sm text-muted-foreground">
                                                        {pillar.count.toLocaleString()}
                                                    </span>
                                                </div>
                                                <div className="flex items-center justify-between mt-1">
                                                    <div className="flex items-center gap-2">
                                                        <div className="w-20 h-2 bg-secondary rounded-full overflow-hidden">
                                                            <div
                                                                className="h-full transition-all duration-300"
                                                                style={{
                                                                    width: `${pillar.percentage * 5}%`,
                                                                    backgroundColor: pillar.color,
                                                                }}
                                                            />
                                                        </div>
                                                        <span className="text-xs text-muted-foreground">
                                                            {pillar.percentage}%
                                                        </span>
                                                    </div>
                                                    <div className="flex items-center">
                                                        {pillar.trend > 0 ? (
                                                            <ArrowTrendingUpIcon className="h-3 w-3 text-success mr-1" />
                                                        ) : (
                                                            <ArrowTrendingDownIcon className="h-3 w-3 text-destructive mr-1" />
                                                        )}
                                                        <span
                                                            className={`text-xs ${pillar.trend > 0 ? "text-success" : "text-destructive"}`}
                                                        >
                                                            {pillar.trend > 0 ? "+" : ""}
                                                            {pillar.trend}%
                                                        </span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                ))}
                            </div>
                            <Button variant="outline" size="sm" className="w-full mt-4">
                                Voir tous les piliers
                            </Button>
                        </CardContent>
                    </Card>

                    {/* Time Trends */}
                    <Card className="p-6">
                        <CardHeader className="px-0 pt-0">
                            <div className="flex items-center justify-between">
                                <div>
                                    <CardTitle>Évolution temporelle</CardTitle>
                                    <CardDescription>
                                        Expressions soumises et résolues par mois
                                    </CardDescription>
                                </div>
                                <div className="flex gap-2">
                                    {(["week", "month", "year"] as const).map((period) => (
                                        <Button
                                            key={period}
                                            variant={timeFilter === period ? "primary" : "ghost"}
                                            size="sm"
                                            onClick={() => handleTimeFilterChange(period)}
                                        >
                                            {period === "week"
                                                ? "Semaine"
                                                : period === "month"
                                                  ? "Mois"
                                                  : "Année"}
                                        </Button>
                                    ))}
                                </div>
                            </div>
                        </CardHeader>
                        <CardContent className="px-0">
                            <div className="space-y-4">
                                {mockData.timeData.map((data, index) => (
                                    <div key={index} className="flex items-center justify-between">
                                        <div className="flex items-center gap-4 flex-1">
                                            <span className="text-sm font-medium text-foreground w-20">
                                                {data.month}
                                            </span>
                                            <div className="flex-1 space-y-2">
                                                <div className="flex items-center justify-between">
                                                    <span className="text-xs text-muted-foreground">
                                                        Soumises
                                                    </span>
                                                    <span className="text-xs font-medium">
                                                        {data.expressions}
                                                    </span>
                                                </div>
                                                <div className="w-full h-2 bg-secondary rounded-full overflow-hidden">
                                                    <div
                                                        className="h-full bg-primary transition-all duration-300"
                                                        style={{
                                                            width: `${(data.expressions / 4000) * 100}%`,
                                                        }}
                                                    />
                                                </div>
                                                <div className="flex items-center justify-between">
                                                    <span className="text-xs text-muted-foreground">
                                                        Résolues
                                                    </span>
                                                    <span className="text-xs font-medium">
                                                        {data.resolved}
                                                    </span>
                                                </div>
                                                <div className="w-full h-2 bg-secondary rounded-full overflow-hidden">
                                                    <div
                                                        className="h-full bg-success transition-all duration-300"
                                                        style={{
                                                            width: `${(data.resolved / 4000) * 100}%`,
                                                        }}
                                                    />
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </CardContent>
                    </Card>
                </div>

                {/* Geographic Analysis */}
                <Card className="p-6 mb-8">
                    <CardHeader className="px-0 pt-0">
                        <CardTitle>Analyse géographique</CardTitle>
                        <CardDescription>
                            Participation citoyenne par région française
                        </CardDescription>
                    </CardHeader>
                    <CardContent className="px-0">
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
                            {mockData.geographicData.map((region, index) => (
                                <div
                                    key={index}
                                    className="p-4 border border-border rounded-xl hover:border-primary/50 transition-colors"
                                >
                                    <div className="flex items-center gap-2 mb-3">
                                        <MapPinIcon className="h-4 w-4 text-primary" />
                                        <span className="font-medium text-foreground text-sm">
                                            {region.region}
                                        </span>
                                    </div>
                                    <div className="space-y-2">
                                        <div className="flex justify-between text-xs">
                                            <span className="text-muted-foreground">
                                                Expressions
                                            </span>
                                            <span className="font-medium">
                                                {region.expressions.toLocaleString()}
                                            </span>
                                        </div>
                                        <div className="flex justify-between text-xs">
                                            <span className="text-muted-foreground">
                                                Population
                                            </span>
                                            <span className="font-medium">
                                                {(region.population / 1000000).toFixed(1)}M
                                            </span>
                                        </div>
                                        <div className="flex justify-between text-xs">
                                            <span className="text-muted-foreground">
                                                Taux participation
                                            </span>
                                            <span className="font-medium text-primary">
                                                {region.rate}‰
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            ))}
                        </div>
                    </CardContent>
                </Card>

                {/* Insights */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <Card className="p-6">
                        <CardHeader className="px-0 pt-0">
                            <CardTitle className="flex items-center gap-2">
                                <ExclamationTriangleIcon className="h-5 w-5 text-warning" />
                                Points d'attention
                            </CardTitle>
                        </CardHeader>
                        <CardContent className="px-0">
                            <div className="space-y-4">
                                <div className="p-3 bg-warning/10 border border-warning/20 rounded-lg">
                                    <h4 className="font-medium text-warning mb-2">
                                        Augmentation des problèmes de santé
                                    </h4>
                                    <p className="text-sm text-muted-foreground">
                                        -3.2% d'expressions liées à la santé nécessite une attention
                                        particulière.
                                    </p>
                                </div>
                                <div className="p-3 bg-info/10 border border-info/20 rounded-lg">
                                    <h4 className="font-medium text-info mb-2">
                                        Forte demande environnementale
                                    </h4>
                                    <p className="text-sm text-muted-foreground">
                                        +28.4% d'expressions environnementales, tendance croissante
                                        à surveiller.
                                    </p>
                                </div>
                                <div className="p-3 bg-destructive/10 border border-destructive/20 rounded-lg">
                                    <h4 className="font-medium text-destructive mb-2">
                                        Délais de résolution élevés
                                    </h4>
                                    <p className="text-sm text-muted-foreground">
                                        4.2 jours en moyenne, objectif : réduire à 3 jours.
                                    </p>
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    <Card className="p-6">
                        <CardHeader className="px-0 pt-0">
                            <CardTitle className="flex items-center gap-2">
                                <ArrowTrendingUpIcon className="h-5 w-5 text-success" />
                                Succès remarquables
                            </CardTitle>
                        </CardHeader>
                        <CardContent className="px-0">
                            <div className="space-y-4">
                                <div className="p-3 bg-success/10 border border-success/20 rounded-lg">
                                    <h4 className="font-medium text-success mb-2">
                                        Engagement démocratique en hausse
                                    </h4>
                                    <p className="text-sm text-muted-foreground">
                                        +34.5% d'expressions sur la démocratie, signe d'engagement
                                        citoyen.
                                    </p>
                                </div>
                                <div className="p-3 bg-primary/10 border border-primary/20 rounded-lg">
                                    <h4 className="font-medium text-primary mb-2">
                                        Résolution efficace des transports
                                    </h4>
                                    <p className="text-sm text-muted-foreground">
                                        85% de taux de résolution pour les problèmes de transport.
                                    </p>
                                </div>
                                <div className="p-3 bg-info/10 border border-info/20 rounded-lg">
                                    <h4 className="font-medium text-info mb-2">
                                        Croissance régionale équilibrée
                                    </h4>
                                    <p className="text-sm text-muted-foreground">
                                        Participation homogène sur l'ensemble du territoire
                                        français.
                                    </p>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                </div>
            </div>
        </Layout>
    );
}
