"use server";

import { fetchAnalytics } from "@/lib/server-api";
import { ActionResult } from "@/lib/actions";

export async function getAnalyticsAction(
    timeRange: "week" | "month" | "year" = "month",
): Promise<ActionResult<any>> {
    try {
        const data = await fetchAnalytics(timeRange);
        return {
            success: true,
            data,
        };
    } catch (error) {
        console.error("Error fetching analytics:", error);
        return {
            success: false,
            error: "Failed to fetch analytics data",
        };
    }
}
