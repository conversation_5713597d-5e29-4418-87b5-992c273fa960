"use server";

import { fetchNotifications, fetchNotificationStats } from "@/lib/server-api";
import { ActionResult } from "@/lib/actions";
import { getAuthCookie } from "@/lib/auth/cookies";
import { API_BASE_URL } from "@/lib/constants";

export async function getNotificationsAction(params?: {
    page?: number;
    pageSize?: number;
    type?: string;
    category?: string;
    read?: string;
    timeRange?: string;
}): Promise<ActionResult<any>> {
    try {
        const result = await fetchNotifications(params);
        return {
            success: true,
            data: result,
        };
    } catch (error) {
        console.error("Error fetching notifications:", error);
        return {
            success: false,
            error: "Failed to fetch notifications",
        };
    }
}

export async function getNotificationStatsAction(): Promise<ActionResult<any>> {
    try {
        const stats = await fetchNotificationStats();
        return {
            success: true,
            data: stats,
        };
    } catch (error) {
        console.error("Error fetching notification stats:", error);
        return {
            success: false,
            error: "Failed to fetch notification stats",
        };
    }
}

export async function markNotificationAsReadAction(
    notificationId: string,
): Promise<ActionResult<any>> {
    try {
        const authToken = await getAuthCookie();

        const response = await fetch(`${API_BASE_URL}/notifications/${notificationId}/mark-read`, {
            method: "PUT",
            headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${authToken}`,
            },
        });

        if (!response.ok) {
            throw new Error(`Failed to mark notification as read: ${response.status}`);
        }

        const data = await response.json();
        return {
            success: true,
            data: data.data,
        };
    } catch (error) {
        console.error("Error marking notification as read:", error);
        return {
            success: false,
            error: "Failed to mark notification as read",
        };
    }
}

export async function markAllNotificationsAsReadAction(): Promise<ActionResult<any>> {
    try {
        const authToken = await getAuthCookie();

        const response = await fetch(`${API_BASE_URL}/notifications/mark-all-read`, {
            method: "PUT",
            headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${authToken}`,
            },
        });

        if (!response.ok) {
            throw new Error(`Failed to mark all notifications as read: ${response.status}`);
        }

        const data = await response.json();
        return {
            success: true,
            data: data.data,
        };
    } catch (error) {
        console.error("Error marking all notifications as read:", error);
        return {
            success: false,
            error: "Failed to mark all notifications as read",
        };
    }
}

export async function deleteNotificationAction(notificationId: string): Promise<ActionResult<any>> {
    try {
        const authToken = await getAuthCookie();

        const response = await fetch(`${API_BASE_URL}/notifications/${notificationId}`, {
            method: "DELETE",
            headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${authToken}`,
            },
        });

        if (!response.ok) {
            throw new Error(`Failed to delete notification: ${response.status}`);
        }

        const data = await response.json();
        return {
            success: true,
            data: data.data,
        };
    } catch (error) {
        console.error("Error deleting notification:", error);
        return {
            success: false,
            error: "Failed to delete notification",
        };
    }
}
