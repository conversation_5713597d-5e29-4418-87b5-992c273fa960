"use client";

import React, { useState, useEffect, useCallback, useTransition } from "react";
import Link from "next/link";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/Card";
import Button from "@/components/ui/Button";
import Badge from "@/components/ui/Badge";
import { useNotificationStore } from "@/stores/notificationStore";
import { toast } from "sonner";
import { User, ServerNotification } from "@/types";
import {
    getNotificationsAction,
    getNotificationStatsAction,
    markNotificationAsReadAction,
    markAllNotificationsAsReadAction,
    deleteNotificationAction,
} from "./actions";
import {
    BellIcon,
    CheckIcon,
    XMarkIcon,
    TrashIcon,
    ClockIcon,
    ExclamationTriangleIcon,
    CheckCircleIcon,
    InformationCircleIcon,
    FunnelIcon,
    ArrowPathIcon,
    BellSlashIcon,
    DocumentTextIcon,
    ChatBubbleLeftRightIcon,
} from "@heroicons/react/24/outline";
import { BellIcon as BellIconSolid } from "@heroicons/react/24/solid";
import Layout from "@/components/layout/Layout";

interface NotificationsClientProps {
    user: User;
    initialNotifications?: ServerNotification[];
    initialStats?: any;
}

export function NotificationsClient({
    user,
    initialNotifications = [],
    initialStats,
}: NotificationsClientProps) {
    const { notifications: clientNotifications, removeNotification } = useNotificationStore();

    const [serverNotifications, setServerNotifications] =
        useState<ServerNotification[]>(initialNotifications);
    const [isPending, startTransition] = useTransition();
    const [filters, setFilters] = useState({
        type: "",
        category: "",
        read: "",
    });
    const [timeRange, setTimeRange] = useState<"all" | "today" | "week" | "month">("all");
    const [stats, setStats] = useState(
        initialStats || {
            total: 0,
            unread: 0,
            byCategory: {
                expression: 0,
                moderation: 0,
                system: 0,
                social: 0,
                update: 0,
            },
        },
    );

    const loadNotifications = useCallback(async () => {
        if (
            initialNotifications.length > 0 &&
            !filters.type &&
            !filters.category &&
            !filters.read &&
            timeRange === "all"
        ) {
            // Use initial data if no filters applied
            return;
        }

        startTransition(async () => {
            const params: any = {
                pageSize: 100,
            };

            // Apply filters
            if (filters.type) params.type = filters.type;
            if (filters.category) params.category = filters.category;
            if (filters.read) params.read = filters.read;
            if (timeRange !== "all") params.timeRange = timeRange;

            const result = await getNotificationsAction(params);

            if (result.success && result.data) {
                setServerNotifications(result.data.notifications || []);

                // Update stats
                const statsResult = await getNotificationStatsAction();
                if (statsResult.success && statsResult.data) {
                    setStats(statsResult.data);
                }
            }
        });
    }, [filters, timeRange, initialNotifications]);

    useEffect(() => {
        loadNotifications();
    }, [loadNotifications]);

    const handleMarkAsRead = async (notificationId: string) => {
        startTransition(async () => {
            const result = await markNotificationAsReadAction(notificationId);

            if (result.success) {
                // Update local state
                setServerNotifications((prev) =>
                    prev.map((n) => (n.id === notificationId ? { ...n, read: true } : n)),
                );

                // Update stats
                setStats((prev: any) => ({
                    ...prev,
                    unread: Math.max(0, prev.unread - 1),
                }));
            } else {
                toast.error(result.error || "Impossible de marquer la notification comme lue");
            }
        });
    };

    const handleMarkAllAsRead = async () => {
        startTransition(async () => {
            const result = await markAllNotificationsAsReadAction();

            if (result.success) {
                // Update local state
                setServerNotifications((prev) => prev.map((n) => ({ ...n, read: true })));

                // Update stats
                setStats((prev: any) => ({
                    ...prev,
                    unread: 0,
                }));

                toast.success("Toutes les notifications ont été marquées comme lues");
            } else {
                toast.error(
                    result.error || "Impossible de marquer toutes les notifications comme lues",
                );
            }
        });
    };

    const handleDeleteNotification = async (notificationId: string, documentId: string) => {
        startTransition(async () => {
            const result = await deleteNotificationAction(documentId);

            if (result.success) {
                // Update local state
                setServerNotifications((prev) => prev.filter((n) => n.id !== notificationId));

                // Update stats if notification was unread
                const notification = serverNotifications.find((n) => n.id === notificationId);
                if (notification && !notification.read) {
                    setStats((prev: any) => ({
                        ...prev,
                        unread: Math.max(0, prev.unread - 1),
                    }));
                }

                toast.success("Notification supprimée");
            } else {
                toast.error(result.error || "Impossible de supprimer la notification");
            }
        });
    };

    const handleClearAll = async () => {
        if (!confirm("Êtes-vous sûr de vouloir supprimer toutes les notifications ?")) {
            return;
        }

        try {
            // In a real implementation: await apiClient.delete("/notifications/clear-all");

            setServerNotifications([]);
            setStats({
                total: 0,
                unread: 0,
                byCategory: {
                    expression: 0,
                    moderation: 0,
                    system: 0,
                    social: 0,
                    update: 0,
                },
            });

            toast.success("Toutes les notifications ont été supprimées");
        } catch (error) {
            console.error("Error clearing notifications:", error);
        }
    };

    const getNotificationIcon = (type: ServerNotification["type"]) => {
        switch (type) {
            case "success":
                return <CheckCircleIcon className="h-5 w-5 text-success" />;
            case "warning":
                return <ExclamationTriangleIcon className="h-5 w-5 text-warning" />;
            case "error":
                return <XMarkIcon className="h-5 w-5 text-destructive" />;
            default:
                return <InformationCircleIcon className="h-5 w-5 text-info" />;
        }
    };

    const getCategoryIcon = (category: ServerNotification["category"]) => {
        switch (category) {
            case "expression":
                return <DocumentTextIcon className="h-5 w-5 text-primary" />;
            case "moderation":
                return <CheckCircleIcon className="h-5 w-5 text-warning" />;
            case "social":
                return <ChatBubbleLeftRightIcon className="h-5 w-5 text-info" />;
            case "system":
                return <BellIcon className="h-5 w-5 text-muted-foreground" />;
            case "update":
                return <ArrowPathIcon className="h-5 w-5 text-success" />;
            default:
                return <BellIcon className="h-5 w-5 text-muted-foreground" />;
        }
    };

    const getCategoryLabel = (category: string) => {
        const labels = {
            expression: "Expressions",
            moderation: "Modération",
            social: "Social",
            system: "Système",
            update: "Mises à jour",
        };
        return labels[category as keyof typeof labels] || category;
    };

    const formatTime = (date: string) => {
        const d = new Date(date);
        const now = new Date();
        const diff = now.getTime() - d.getTime();
        const minutes = Math.floor(diff / 60000);
        const hours = Math.floor(diff / 3600000);
        const days = Math.floor(diff / 86400000);

        if (minutes < 1) return "À l'instant";
        if (minutes < 60) return `Il y a ${minutes} min`;
        if (hours < 24) return `Il y a ${hours}h`;
        if (days < 7) return `Il y a ${days}j`;
        return d.toLocaleDateString("fr-FR");
    };

    const filteredNotifications = serverNotifications;

    if (isPending) {
        return (
            <div className="min-h-screen bg-secondary/30 flex items-center justify-center">
                <div className="text-center">
                    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
                    <p className="mt-4 text-muted-foreground">Chargement des notifications...</p>
                </div>
            </div>
        );
    }

    return (
        <Layout showHeader={true} showFooter={false}>
            <div className="bg-secondary/30">
                {/* Header */}
                <section className="civic-gradient text-white py-12">
                    <div className="civic-container">
                        <div className="flex items-center justify-between">
                            <div>
                                <Badge
                                    variant="primary"
                                    size="lg"
                                    className="mb-4 bg-white/20 text-white border-white/30"
                                >
                                    Centre de notifications
                                </Badge>
                                <h1 className="text-4xl font-bold mb-4 font-marianne">
                                    Vos notifications
                                </h1>
                                <p className="text-xl opacity-90 max-w-2xl">
                                    Restez informé de l'activité de vos expressions et des mises à
                                    jour importantes
                                </p>
                            </div>
                            <div className="flex items-center gap-4">
                                <Button
                                    variant="secondary"
                                    icon={<ArrowPathIcon className="h-4 w-4" />}
                                    onClick={loadNotifications}
                                >
                                    Actualiser
                                </Button>
                                {stats.unread > 0 && (
                                    <Button
                                        variant="outline"
                                        className="border-white text-white hover:bg-white hover:text-primary"
                                        icon={<CheckIcon className="h-4 w-4" />}
                                        onClick={handleMarkAllAsRead}
                                    >
                                        Tout marquer lu
                                    </Button>
                                )}
                            </div>
                        </div>
                    </div>
                </section>

                <div className="civic-container py-8">
                    {/* Stats Cards */}
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-8">
                        <Card className="p-6">
                            <div className="flex items-center justify-between">
                                <div>
                                    <p className="text-sm text-muted-foreground">Total</p>
                                    <p className="text-3xl font-bold text-foreground">
                                        {stats.total}
                                    </p>
                                </div>
                                <BellIcon className="h-8 w-8 text-primary" />
                            </div>
                        </Card>

                        <Card className="p-6">
                            <div className="flex items-center justify-between">
                                <div>
                                    <p className="text-sm text-muted-foreground">Non lues</p>
                                    <p className="text-3xl font-bold text-warning">
                                        {stats.unread}
                                    </p>
                                </div>
                                <BellIconSolid className="h-8 w-8 text-warning" />
                            </div>
                        </Card>

                        <Card className="p-6">
                            <div className="flex items-center justify-between">
                                <div>
                                    <p className="text-sm text-muted-foreground">Expressions</p>
                                    <p className="text-3xl font-bold text-primary">
                                        {stats.byCategory.expression}
                                    </p>
                                </div>
                                <DocumentTextIcon className="h-8 w-8 text-primary" />
                            </div>
                        </Card>

                        <Card className="p-6">
                            <div className="flex items-center justify-between">
                                <div>
                                    <p className="text-sm text-muted-foreground">Social</p>
                                    <p className="text-3xl font-bold text-info">
                                        {stats.byCategory.social}
                                    </p>
                                </div>
                                <ChatBubbleLeftRightIcon className="h-8 w-8 text-info" />
                            </div>
                        </Card>

                        <Card className="p-6">
                            <div className="flex items-center justify-between">
                                <div>
                                    <p className="text-sm text-muted-foreground">Système</p>
                                    <p className="text-3xl font-bold text-muted-foreground">
                                        {stats.byCategory.system}
                                    </p>
                                </div>
                                <InformationCircleIcon className="h-8 w-8 text-muted-foreground" />
                            </div>
                        </Card>
                    </div>

                    {/* Filters */}
                    <Card className="mb-8">
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <FunnelIcon className="h-5 w-5" />
                                Filtres
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                                <div>
                                    <label className="block text-sm font-medium text-muted-foreground mb-2">
                                        Période
                                    </label>
                                    <select
                                        value={timeRange}
                                        onChange={(e) => setTimeRange(e.target.value as any)}
                                        className="w-full px-3 py-2 border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
                                    >
                                        <option value="all">Toutes</option>
                                        <option value="today">Aujourd'hui</option>
                                        <option value="week">Cette semaine</option>
                                        <option value="month">Ce mois</option>
                                    </select>
                                </div>

                                <div>
                                    <label className="block text-sm font-medium text-muted-foreground mb-2">
                                        Catégorie
                                    </label>
                                    <select
                                        value={filters.category}
                                        onChange={(e) =>
                                            setFilters({ ...filters, category: e.target.value })
                                        }
                                        className="w-full px-3 py-2 border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
                                    >
                                        <option value="">Toutes</option>
                                        <option value="expression">Expressions</option>
                                        <option value="moderation">Modération</option>
                                        <option value="social">Social</option>
                                        <option value="system">Système</option>
                                        <option value="update">Mises à jour</option>
                                    </select>
                                </div>

                                <div>
                                    <label className="block text-sm font-medium text-muted-foreground mb-2">
                                        Type
                                    </label>
                                    <select
                                        value={filters.type}
                                        onChange={(e) =>
                                            setFilters({ ...filters, type: e.target.value })
                                        }
                                        className="w-full px-3 py-2 border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
                                    >
                                        <option value="">Tous</option>
                                        <option value="info">Information</option>
                                        <option value="success">Succès</option>
                                        <option value="warning">Avertissement</option>
                                        <option value="error">Erreur</option>
                                    </select>
                                </div>

                                <div>
                                    <label className="block text-sm font-medium text-muted-foreground mb-2">
                                        Statut
                                    </label>
                                    <select
                                        value={filters.read}
                                        onChange={(e) =>
                                            setFilters({ ...filters, read: e.target.value })
                                        }
                                        className="w-full px-3 py-2 border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
                                    >
                                        <option value="">Tous</option>
                                        <option value="false">Non lues</option>
                                        <option value="true">Lues</option>
                                    </select>
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Notifications List */}
                    <div className="space-y-4">
                        {filteredNotifications.length === 0 ? (
                            <Card className="p-12 text-center">
                                <BellSlashIcon className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
                                <h3 className="text-xl font-semibold text-foreground mb-2">
                                    Aucune notification
                                </h3>
                                <p className="text-muted-foreground">
                                    Vous n'avez aucune notification pour le moment
                                </p>
                            </Card>
                        ) : (
                            <>
                                <div className="flex items-center justify-between mb-4">
                                    <h2 className="text-lg font-semibold text-foreground">
                                        {filteredNotifications.length} notification
                                        {filteredNotifications.length > 1 ? "s" : ""}
                                    </h2>
                                    {filteredNotifications.length > 0 && (
                                        <Button
                                            variant="ghost"
                                            size="sm"
                                            onClick={handleClearAll}
                                            icon={<TrashIcon className="h-4 w-4" />}
                                        >
                                            Tout supprimer
                                        </Button>
                                    )}
                                </div>

                                {filteredNotifications.map((notification) => (
                                    <Card
                                        key={notification.id}
                                        className={`p-6 transition-all ${
                                            !notification.read
                                                ? "bg-primary/5 border-primary/20"
                                                : ""
                                        }`}
                                    >
                                        <div className="flex items-start gap-4">
                                            <div className="flex-shrink-0 mt-1">
                                                {getNotificationIcon(notification.type)}
                                            </div>

                                            <div className="flex-1 min-w-0">
                                                <div className="flex items-start justify-between">
                                                    <div className="flex-1">
                                                        <div className="flex items-center gap-2 mb-1">
                                                            <h3 className="font-semibold text-foreground">
                                                                {notification.title}
                                                            </h3>
                                                            {!notification.read && (
                                                                <Badge variant="primary" size="sm">
                                                                    Nouveau
                                                                </Badge>
                                                            )}
                                                        </div>
                                                        <p className="text-muted-foreground mb-3">
                                                            {notification.message}
                                                        </p>
                                                    </div>

                                                    <Button
                                                        variant="ghost"
                                                        size="xs"
                                                        onClick={() =>
                                                            handleDeleteNotification(
                                                                notification.id,
                                                                notification.documentId,
                                                            )
                                                        }
                                                        className="ml-4"
                                                    >
                                                        <XMarkIcon className="h-4 w-4" />
                                                    </Button>
                                                </div>

                                                <div className="flex items-center justify-between">
                                                    <div className="flex items-center gap-4 text-sm text-muted-foreground">
                                                        <span className="flex items-center gap-1">
                                                            {getCategoryIcon(notification.category)}
                                                            {getCategoryLabel(
                                                                notification.category,
                                                            )}
                                                        </span>
                                                        <span className="flex items-center gap-1">
                                                            <ClockIcon className="h-4 w-4" />
                                                            {formatTime(notification.createdAt)}
                                                        </span>
                                                    </div>

                                                    <div className="flex items-center gap-2">
                                                        {!notification.read && (
                                                            <Button
                                                                variant="ghost"
                                                                size="sm"
                                                                onClick={() =>
                                                                    handleMarkAsRead(
                                                                        notification.id,
                                                                    )
                                                                }
                                                            >
                                                                Marquer comme lu
                                                            </Button>
                                                        )}
                                                        {notification.metadata?.actionUrl && (
                                                            <Button
                                                                variant="primary"
                                                                size="sm"
                                                                as={Link}
                                                                href={
                                                                    notification.metadata.actionUrl
                                                                }
                                                            >
                                                                {notification.metadata
                                                                    .actionLabel || "Voir"}
                                                            </Button>
                                                        )}
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </Card>
                                ))}
                            </>
                        )}
                    </div>

                    {/* Client-side notifications section */}
                    {clientNotifications.length > 0 && (
                        <div className="mt-12">
                            <h2 className="text-xl font-bold text-foreground mb-6">
                                Notifications récentes (session)
                            </h2>
                            <div className="space-y-4">
                                {clientNotifications.map((notification) => (
                                    <Card
                                        key={notification.id}
                                        className={`p-4 transition-all ${
                                            !notification.read
                                                ? "bg-primary/5 border-primary/20"
                                                : ""
                                        }`}
                                    >
                                        <div className="flex items-start gap-3">
                                            <div className="flex-shrink-0">
                                                {getNotificationIcon(notification.type)}
                                            </div>
                                            <div className="flex-1">
                                                <h4 className="font-medium text-foreground">
                                                    {notification.title}
                                                </h4>
                                                <p className="text-sm text-muted-foreground mt-1">
                                                    {notification.message}
                                                </p>
                                                <div className="flex items-center justify-between mt-2">
                                                    <span className="text-xs text-muted-foreground">
                                                        {formatTime(
                                                            notification.timestamp.toISOString(),
                                                        )}
                                                    </span>
                                                    {notification.actionUrl && (
                                                        <Button
                                                            variant="ghost"
                                                            size="xs"
                                                            as={Link}
                                                            href={notification.actionUrl}
                                                        >
                                                            {notification.actionLabel || "Voir"}
                                                        </Button>
                                                    )}
                                                </div>
                                            </div>

                                            <Button
                                                variant="ghost"
                                                size="xs"
                                                onClick={() => removeNotification(notification.id)}
                                            >
                                                <XMarkIcon className="h-4 w-4" />
                                            </Button>
                                        </div>
                                    </Card>
                                ))}
                            </div>
                        </div>
                    )}
                </div>
            </div>
        </Layout>
    );
}
