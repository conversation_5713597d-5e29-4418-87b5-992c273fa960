import { redirect } from "next/navigation";
import { getCurrentUser } from "@/lib/auth/server";
import { fetchNotifications, fetchNotificationStats } from "@/lib/server-api";
import { NotificationsClient } from "./notifications-client";

export default async function NotificationsPage() {
    const user = await getCurrentUser();

    if (!user) {
        redirect("/auth/login");
    }

    // Fetch notifications and stats
    let notifications = [];
    let stats = null;

    try {
        const [notifData, statsData] = await Promise.all([
            fetchNotifications({ pageSize: 25 }),
            fetchNotificationStats(),
        ]);

        notifications = notifData.notifications;
        stats = statsData;
    } catch (error) {
        console.error("Error fetching notifications:", error);
    }

    return (
        <NotificationsClient
            user={user}
            initialNotifications={notifications}
            initialStats={stats}
        />
    );
}
