"use client";

import React, { useState } from "react";
import { useRouter } from "next/navigation";
import { createExpressionAction } from "@/lib/actions";
import { ExpressionFormData, Pilier, SousPilier, Entite, Lieu, User } from "@/types";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/Card";
import Button from "@/components/ui/Button";
import Input from "@/components/ui/Input";
import LocationPicker from "@/components/maps/LocationPicker";
import FileUpload, { UploadedFile } from "@/components/forms/FileUpload";
import { PilierIcon } from "@/components/icons/PilierIcon";
import { toast } from "sonner";
import {
    CheckCircleIcon,
    XMarkIcon,
    InformationCircleIcon,
    CalendarIcon,
    TagIcon,
    BuildingOfficeIcon,
} from "@heroicons/react/24/outline";

interface NewExpressionClientProps {
    user: User;
    piliers: Pilier[];
    sousPiliers: SousPilier[];
    entites: Entite[];
    initialPilierId?: string;
}

export function NewExpressionClient({
    piliers,
    sousPiliers,
    entites,
    initialPilierId,
}: NewExpressionClientProps) {
    const router = useRouter();
    const [isLoading, setIsLoading] = useState(false);
    const [files, setFiles] = useState<UploadedFile[]>([]);

    // Initialize selected piliers with the initialPilierId if it's valid
    const validInitialPilier =
        initialPilierId && piliers.some((p) => p.documentId === initialPilierId);

    const [selectedPiliers, setSelectedPiliers] = useState<string[]>(
        validInitialPilier ? [initialPilierId] : [],
    );
    const [selectedSousPiliers, setSelectedSousPiliers] = useState<string[]>([]);
    const [selectedEntites, setSelectedEntites] = useState<string[]>([]);
    const [tags, setTags] = useState<string[]>([]);
    const [tagInput, setTagInput] = useState("");

    const [formData, setFormData] = useState<Partial<ExpressionFormData>>({
        titre: "",
        contenu: "",
        type_expression: "probleme",
        urgence: 3,
        etat_emotionnel: "neutre",
        date_evenement: new Date().toISOString().split("T")[0],
        lieu: "",
        piliers: [],
        sous_piliers: [],
        entites: [],
        tags: [],
    });

    const [location, setLocation] = useState<Lieu | null>(null);

    // No need to load data on mount, it's passed from server

    // Filter sous-piliers based on selected piliers
    const filteredSousPiliers = sousPiliers.filter((sp) => {
        const pilierId = typeof sp.pilier === "string" ? sp.pilier : sp.pilier?.documentId;
        return selectedPiliers.includes(pilierId || "");
    });

    const handleInputChange = (
        e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>,
    ) => {
        const { name, value } = e.target;
        setFormData((prev) => ({
            ...prev,
            [name]: name === "urgence" ? parseInt(value) : value,
        }));
    };

    const handleLocationSelect = (loc: Lieu | null) => {
        setLocation(loc);
        if (loc) {
            setFormData((prev) => ({
                ...prev,
                lieu: loc.documentId,
                geolocation: {
                    lat: loc.coordonnees.lat,
                    lng: loc.coordonnees.lng,
                },
            }));
        }
    };

    const handleFilesChange = (updatedFiles: UploadedFile[]) => {
        setFiles(updatedFiles);
    };

    const togglePilier = (pilierId: string) => {
        setSelectedPiliers((prev) =>
            prev.includes(pilierId) ? prev.filter((id) => id !== pilierId) : [...prev, pilierId],
        );
    };

    const toggleSousPilier = (sousPilierId: string) => {
        setSelectedSousPiliers((prev) =>
            prev.includes(sousPilierId)
                ? prev.filter((id) => id !== sousPilierId)
                : [...prev, sousPilierId],
        );
    };

    const toggleEntite = (entiteId: string) => {
        setSelectedEntites((prev) =>
            prev.includes(entiteId) ? prev.filter((id) => id !== entiteId) : [...prev, entiteId],
        );
    };

    const addTag = () => {
        if (tagInput.trim() && !tags.includes(tagInput.trim())) {
            setTags([...tags, tagInput.trim()]);
            setTagInput("");
        }
    };

    const removeTag = (tag: string) => {
        setTags(tags.filter((t) => t !== tag));
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();

        if (!formData.titre || !formData.contenu) {
            toast.error("Veuillez remplir tous les champs obligatoires");
            return;
        }

        if (!location) {
            toast.error("Veuillez sélectionner un lieu");
            return;
        }

        if (selectedPiliers.length === 0) {
            toast.error("Veuillez sélectionner au moins un pilier");
            return;
        }

        setIsLoading(true);

        try {
            // Prepare the data for submission
            const submissionData: ExpressionFormData = {
                titre: formData.titre!,
                contenu: formData.contenu!,
                type_expression: formData.type_expression as ExpressionFormData["type_expression"],
                urgence: formData.urgence as ExpressionFormData["urgence"],
                etat_emotionnel: formData.etat_emotionnel as ExpressionFormData["etat_emotionnel"],
                date_evenement: formData.date_evenement!,
                lieu: formData.lieu!,
                geolocation: formData.geolocation,
                piliers: selectedPiliers,
                sous_piliers: selectedSousPiliers,
                entites: selectedEntites,
                tags: tags,
                medias: files.filter((f) => f.uploaded && f.strapiId).map((f) => f.strapiId!),
            };

            // Create FormData for server action
            const formDataToSend = new FormData();
            for (const [key, value] of Object.entries(submissionData)) {
                if (typeof value === "object") {
                    formDataToSend.set(key, JSON.stringify(value));
                } else if (value !== undefined && value !== null) {
                    formDataToSend.set(key, value.toString());
                }
            }
            const result = await createExpressionAction(formDataToSend);

            if (result.success) {
                toast.success("Expression créée avec succès !");
                router.push(`/expressions/my-expressions?success=created`);
            } else {
                toast.error(result.error || "Erreur lors de la création de l'expression");
            }
        } catch (error) {
            console.error("Error creating expression:", error);
            toast.error("Erreur lors de la création de l'expression");
        } finally {
            setIsLoading(false);
        }
    };

    const getUrgenceLabel = (urgence: number) => {
        const labels = {
            1: "Très faible",
            2: "Faible",
            3: "Modérée",
            4: "Élevée",
            5: "Critique",
        };
        return labels[urgence as keyof typeof labels];
    };

    const getUrgenceColor = (urgence: number) => {
        const colors = {
            1: "bg-gray-100 text-gray-800",
            2: "bg-blue-100 text-blue-800",
            3: "bg-yellow-100 text-yellow-800",
            4: "bg-orange-100 text-orange-800",
            5: "bg-red-100 text-red-800",
        };
        return colors[urgence as keyof typeof colors];
    };

    return (
        <div className="min-h-screen bg-gray-50 py-8">
            <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
                {/* Page Header */}
                <div className="mb-8">
                    <h1 className="text-3xl font-bold text-gray-900 mb-2">
                        Nouvelle Expression Citoyenne
                    </h1>
                    <p className="text-gray-600">
                        Partagez votre expérience, problème ou suggestion pour améliorer notre
                        société.
                    </p>
                </div>

                <form onSubmit={handleSubmit} className="space-y-6">
                    {/* Basic Information */}
                    <Card>
                        <CardHeader>
                            <CardTitle>Informations de base</CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                    Titre *
                                </label>
                                <Input
                                    type="text"
                                    name="titre"
                                    value={formData.titre}
                                    onChange={handleInputChange}
                                    placeholder="Décrivez brièvement votre expression"
                                    required
                                    maxLength={200}
                                />
                                <p className="mt-1 text-xs text-gray-500">
                                    {formData.titre?.length || 0}/200 caractères
                                </p>
                            </div>

                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                    Description détaillée *
                                </label>
                                <textarea
                                    name="contenu"
                                    value={formData.contenu}
                                    onChange={handleInputChange}
                                    rows={6}
                                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent resize-none"
                                    placeholder="Expliquez en détail votre situation, problème ou suggestion..."
                                    required
                                    maxLength={2000}
                                />
                                <p className="mt-1 text-xs text-gray-500">
                                    {formData.contenu?.length || 0}/2000 caractères
                                </p>
                            </div>

                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-1">
                                        Type d'expression *
                                    </label>
                                    <select
                                        name="type_expression"
                                        value={formData.type_expression}
                                        onChange={handleInputChange}
                                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                                        required
                                    >
                                        <option value="probleme">Problème</option>
                                        <option value="satisfaction">Satisfaction</option>
                                        <option value="idee">Idée</option>
                                        <option value="question">Question</option>
                                    </select>
                                </div>

                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-1">
                                        État émotionnel
                                    </label>
                                    <select
                                        name="etat_emotionnel"
                                        value={formData.etat_emotionnel}
                                        onChange={handleInputChange}
                                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                                    >
                                        <option value="neutre">Neutre</option>
                                        <option value="colere">Colère</option>
                                        <option value="joie">Joie</option>
                                        <option value="tristesse">Tristesse</option>
                                        <option value="espoir">Espoir</option>
                                        <option value="frustration">Frustration</option>
                                    </select>
                                </div>
                            </div>

                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                    Niveau d'urgence *
                                </label>
                                <div className="flex items-center space-x-4">
                                    {[1, 2, 3, 4, 5].map((level) => (
                                        <button
                                            key={level}
                                            type="button"
                                            onClick={() =>
                                                setFormData((prev) => ({
                                                    ...prev,
                                                    urgence: level as 1 | 2 | 3 | 4 | 5,
                                                }))
                                            }
                                            className={`px-4 py-2 rounded-lg font-medium transition-all ${
                                                formData.urgence === level
                                                    ? getUrgenceColor(level)
                                                    : "bg-gray-100 text-gray-600 hover:bg-gray-200"
                                            }`}
                                        >
                                            {level}
                                        </button>
                                    ))}
                                </div>
                                <p className="mt-2 text-sm text-gray-600">
                                    Niveau sélectionné:{" "}
                                    <span className="font-medium">
                                        {getUrgenceLabel(formData.urgence!)}
                                    </span>
                                </p>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Location and Date */}
                    <Card>
                        <CardHeader>
                            <CardTitle>Localisation et date</CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                    Lieu de l'événement *
                                </label>
                                <LocationPicker
                                    onLocationSelect={handleLocationSelect}
                                    placeholder="Recherchez une ville ou une adresse..."
                                    required
                                />
                            </div>

                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                    <CalendarIcon className="inline-block w-4 h-4 mr-1" />
                                    Date de l'événement
                                </label>
                                <Input
                                    type="date"
                                    name="date_evenement"
                                    value={formData.date_evenement}
                                    onChange={handleInputChange}
                                    max={new Date().toISOString().split("T")[0]}
                                />
                            </div>
                        </CardContent>
                    </Card>

                    {/* Pillars Selection */}
                    <Card>
                        <CardHeader>
                            <CardTitle>Piliers concernés *</CardTitle>
                            <p className="text-sm text-gray-500">
                                Sélectionnez un ou plusieurs piliers concernés par votre expression
                            </p>
                            {validInitialPilier && (
                                <div className="mt-2 p-3 bg-blue-50 text-blue-700 rounded-lg text-sm flex items-center">
                                    <InformationCircleIcon className="h-5 w-5 mr-2" />
                                    Le pilier "
                                    {piliers.find((p) => p.documentId === initialPilierId)?.nom}" a
                                    été pré-sélectionné
                                </div>
                            )}
                        </CardHeader>
                        <CardContent>
                            <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                                {piliers.map((pilier) => (
                                    <button
                                        key={pilier.documentId}
                                        type="button"
                                        onClick={() => togglePilier(pilier.documentId)}
                                        className={`p-3 rounded-lg border-2 transition-all ${
                                            selectedPiliers.includes(pilier.documentId)
                                                ? "border-blue-500 bg-blue-50"
                                                : "border-gray-200 hover:border-gray-300"
                                        }`}
                                    >
                                        <div className="flex items-center space-x-2">
                                            <PilierIcon
                                                iconName={pilier.icone}
                                                className="h-6 w-6"
                                                style={{ color: pilier.couleur }}
                                            />
                                            <span className="text-sm font-medium">
                                                {pilier.nom}
                                            </span>
                                        </div>
                                    </button>
                                ))}
                            </div>

                            {/* Sub-pillars */}
                            {filteredSousPiliers.length > 0 && (
                                <div className="mt-6">
                                    <h4 className="text-sm font-medium text-gray-700 mb-3">
                                        Sous-piliers (optionnel)
                                    </h4>
                                    <div className="flex flex-wrap gap-2">
                                        {filteredSousPiliers.map((sousPilier) => (
                                            <button
                                                key={sousPilier.documentId}
                                                type="button"
                                                onClick={() =>
                                                    toggleSousPilier(sousPilier.documentId)
                                                }
                                                className={`px-3 py-1 rounded-full text-sm transition-all ${
                                                    selectedSousPiliers.includes(
                                                        sousPilier.documentId,
                                                    )
                                                        ? "bg-blue-100 text-blue-800 border-blue-300"
                                                        : "bg-gray-100 text-gray-700 hover:bg-gray-200"
                                                } border`}
                                            >
                                                {sousPilier.nom}
                                            </button>
                                        ))}
                                    </div>
                                </div>
                            )}
                        </CardContent>
                    </Card>

                    {/* Entities */}
                    <Card>
                        <CardHeader>
                            <CardTitle>
                                <BuildingOfficeIcon className="inline-block w-5 h-5 mr-2" />
                                Entités concernées (optionnel)
                            </CardTitle>
                            <p className="text-sm text-gray-500">
                                Mentionnez les organisations ou services impliqués
                            </p>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-2">
                                {entites.slice(0, 10).map((entite) => (
                                    <label
                                        key={entite.documentId}
                                        className="flex items-center space-x-3 cursor-pointer hover:bg-gray-50 p-2 rounded"
                                    >
                                        <input
                                            type="checkbox"
                                            checked={selectedEntites.includes(entite.documentId)}
                                            onChange={() => toggleEntite(entite.documentId)}
                                            className="h-4 w-4 text-blue-600 rounded focus:ring-blue-500"
                                        />
                                        <div className="flex-1">
                                            <p className="text-sm font-medium text-gray-900">
                                                {entite.nom}
                                            </p>
                                            {entite.description && (
                                                <p className="text-xs text-gray-500">
                                                    {entite.description}
                                                </p>
                                            )}
                                        </div>
                                    </label>
                                ))}
                            </div>
                        </CardContent>
                    </Card>

                    {/* Tags */}
                    <Card>
                        <CardHeader>
                            <CardTitle>
                                <TagIcon className="inline-block w-5 h-5 mr-2" />
                                Tags (optionnel)
                            </CardTitle>
                            <p className="text-sm text-gray-500">
                                Ajoutez des mots-clés pour faciliter la recherche
                            </p>
                        </CardHeader>
                        <CardContent className="space-y-3">
                            <div className="flex gap-2">
                                <Input
                                    type="text"
                                    value={tagInput}
                                    onChange={(e) => setTagInput(e.target.value)}
                                    onKeyDown={(e) => {
                                        if (e.key === "Enter") {
                                            e.preventDefault();
                                            addTag();
                                        }
                                    }}
                                    placeholder="Ajouter un tag..."
                                    className="flex-1"
                                />
                                <Button type="button" onClick={addTag} variant="outline">
                                    Ajouter
                                </Button>
                            </div>
                            {tags.length > 0 && (
                                <div className="flex flex-wrap gap-2">
                                    {tags.map((tag) => (
                                        <span
                                            key={tag}
                                            className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-gray-100 text-gray-700"
                                        >
                                            {tag}
                                            <button
                                                type="button"
                                                onClick={() => removeTag(tag)}
                                                className="ml-2 text-gray-500 hover:text-gray-700"
                                            >
                                                <XMarkIcon className="h-3 w-3" />
                                            </button>
                                        </span>
                                    ))}
                                </div>
                            )}
                        </CardContent>
                    </Card>

                    {/* File Upload */}
                    <Card onClick={(e) => e.stopPropagation()}>
                        <CardHeader>
                            <CardTitle>Pièces jointes (optionnel)</CardTitle>
                            <p className="text-sm text-gray-500">
                                Ajoutez des photos ou documents pour illustrer votre expression
                            </p>
                        </CardHeader>
                        <CardContent>
                            <FileUpload
                                onFilesChange={handleFilesChange}
                                acceptedTypes={["image/*", "application/pdf", ".doc", ".docx"]}
                                maxFiles={5}
                                maxSize={10}
                            />
                        </CardContent>
                    </Card>

                    {/* Submit Buttons */}
                    <div className="flex justify-between items-center pt-6">
                        <Button
                            type="button"
                            variant="outline"
                            onClick={() => router.push("/expressions")}
                            disabled={isLoading}
                        >
                            Annuler
                        </Button>

                        <div className="flex gap-3">
                            <Button
                                type="button"
                                variant="secondary"
                                disabled={isLoading}
                                onClick={() => toast.info("Brouillon non implémenté")}
                            >
                                Enregistrer comme brouillon
                            </Button>
                            <Button
                                type="submit"
                                disabled={isLoading}
                                className="bg-blue-600 hover:bg-blue-700 text-white"
                            >
                                {isLoading ? (
                                    <>
                                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                                        Envoi en cours...
                                    </>
                                ) : (
                                    <>
                                        <CheckCircleIcon className="h-5 w-5 mr-2" />
                                        Soumettre l'expression
                                    </>
                                )}
                            </Button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    );
}
