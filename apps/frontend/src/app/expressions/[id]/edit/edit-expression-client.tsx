"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { updateExpressionAction } from "@/lib/actions";
import { Expression, ExpressionFormData, Pilier, SousPilier, Entite, Lieu, User } from "@/types";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/Card";
import Button from "@/components/ui/Button";
import Input from "@/components/ui/Input";
import LocationPicker from "@/components/maps/LocationPicker";
import FileUpload, { UploadedFile } from "@/components/forms/FileUpload";
import { PilierIcon } from "@/components/icons/PilierIcon";
import { toast } from "sonner";
import {
    CheckCircleIcon,
    XMarkIcon,
    InformationCircleIcon,
    CalendarIcon,
    TagIcon,
    BuildingOfficeIcon,
    DocumentIcon,
    EyeIcon,
} from "@heroicons/react/24/outline";
import { assetUrl } from "@/lib/constants";
import Link from "next/link";

interface EditExpressionClientProps {
    user: User;
    expression: Expression;
    piliers: Pilier[];
    sousPiliers: SousPilier[];
    entites: Entite[];
}

export function EditExpressionClient({
    expression,
    piliers,
    sousPiliers,
    entites,
}: EditExpressionClientProps) {
    const router = useRouter();
    const [isLoading, setIsLoading] = useState(false);

    // Only new files uploaded during editing
    const [files, setFiles] = useState<UploadedFile[]>([]);

    // Track which existing media to keep
    const [existingMediaToKeep, setExistingMediaToKeep] = useState<string[]>(() => {
        if (expression.medias && expression.medias.length > 0) {
            return expression.medias.map((media: any) =>
                typeof media === "object" ? media.id : media,
            );
        }
        return [];
    });

    const [selectedPiliers, setSelectedPiliers] = useState<string[]>(
        expression.piliers?.map((p: any) => p.documentId || p) || [],
    );
    const [selectedSousPiliers, setSelectedSousPiliers] = useState<string[]>(
        expression.sous_piliers?.map((sp: any) => sp.documentId || sp) || [],
    );
    const [selectedEntites, setSelectedEntites] = useState<string[]>(
        expression.entites?.map((e: any) => e.documentId || e) || [],
    );
    const [tags, setTags] = useState<string[]>(expression.tags || []);
    const [tagInput, setTagInput] = useState("");

    const [formData, setFormData] = useState<Partial<ExpressionFormData>>({
        titre: expression.titre,
        contenu: expression.contenu,
        type_expression: expression.type_expression,
        urgence: expression.urgence,
        etat_emotionnel: expression.etat_emotionnel,
        date_evenement:
            expression.date_evenement?.split("T")[0] || new Date().toISOString().split("T")[0],
        lieu:
            typeof expression.lieu === "string"
                ? expression.lieu
                : expression.lieu?.documentId || "",
        geolocation: expression.geolocation as { lat: number; lng: number } | undefined,
        piliers: selectedPiliers,
        sous_piliers: selectedSousPiliers,
        entites: selectedEntites,
        tags: expression.tags || [],
    });

    const [location, setLocation] = useState<Lieu | null>(null);

    // Set location if available on mount
    useEffect(() => {
        if (typeof expression.lieu !== "string" && expression.lieu) {
            // We have a full lieu object
            setLocation(expression.lieu);
        } else if (expression.geolocation) {
            // Create a basic location object from geolocation data
            const lieuName = typeof expression.lieu === "string" ? expression.lieu : "Localisation";
            setLocation({
                documentId: lieuName,
                nom: lieuName,
                type: "adresse" as const,
                niveau: "ville" as const,
                pays: "FR",
                actif: true,
                verifie: true,
                coordonnees: {
                    lat: expression.geolocation.lat,
                    lng: expression.geolocation.lng,
                },
            });
        }
    }, [expression.lieu, expression.geolocation]);

    // Filter sous-piliers based on selected piliers
    const filteredSousPiliers = sousPiliers.filter((sp) => {
        const pilierId = typeof sp.pilier === "string" ? sp.pilier : sp.pilier?.documentId;
        return selectedPiliers.includes(pilierId || "");
    });

    const handleInputChange = (
        e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>,
    ) => {
        const { name, value } = e.target;
        setFormData((prev) => ({
            ...prev,
            [name]: name === "urgence" ? parseInt(value) : value,
        }));
    };

    const handleLocationSelect = (loc: Lieu | null) => {
        setLocation(loc);
        if (loc) {
            setFormData((prev) => ({
                ...prev,
                lieu: loc.documentId,
                geolocation: {
                    lat: loc.coordonnees.lat,
                    lng: loc.coordonnees.lng,
                },
            }));
        }
    };

    const handleFilesChange = (updatedFiles: UploadedFile[]) => {
        setFiles(updatedFiles);
    };

    const togglePilier = (pilierId: string) => {
        setSelectedPiliers((prev) =>
            prev.includes(pilierId) ? prev.filter((id) => id !== pilierId) : [...prev, pilierId],
        );
    };

    const toggleSousPilier = (sousPilierId: string) => {
        setSelectedSousPiliers((prev) =>
            prev.includes(sousPilierId)
                ? prev.filter((id) => id !== sousPilierId)
                : [...prev, sousPilierId],
        );
    };

    const toggleEntite = (entiteId: string) => {
        setSelectedEntites((prev) =>
            prev.includes(entiteId) ? prev.filter((id) => id !== entiteId) : [...prev, entiteId],
        );
    };

    const addTag = () => {
        if (tagInput.trim() && !tags.includes(tagInput.trim())) {
            setTags([...tags, tagInput.trim()]);
            setTagInput("");
        }
    };

    const removeTag = (tag: string) => {
        setTags(tags.filter((t) => t !== tag));
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();

        if (!formData.titre || !formData.contenu) {
            toast.error("Veuillez remplir tous les champs obligatoires");
            return;
        }

        if (!location) {
            toast.error("Veuillez sélectionner un lieu");
            return;
        }

        if (selectedPiliers.length === 0) {
            toast.error("Veuillez sélectionner au moins un pilier");
            return;
        }

        setIsLoading(true);

        try {
            // Prepare the data for submission
            const submissionData: ExpressionFormData = {
                titre: formData.titre!,
                contenu: formData.contenu!,
                type_expression: formData.type_expression as ExpressionFormData["type_expression"],
                urgence: formData.urgence as ExpressionFormData["urgence"],
                etat_emotionnel: formData.etat_emotionnel as ExpressionFormData["etat_emotionnel"],
                date_evenement: formData.date_evenement!,
                lieu: formData.lieu!,
                geolocation: formData.geolocation,
                piliers: selectedPiliers,
                sous_piliers: selectedSousPiliers,
                entites: selectedEntites,
                tags: tags,
                medias: [
                    ...existingMediaToKeep,
                    ...files.filter((f) => f.uploaded && f.strapiId).map((f) => f.strapiId!),
                ],
            };

            const formDataToSend = new FormData();

            for (const [key, value] of Object.entries(submissionData)) {
                if (typeof value === "object") {
                    formDataToSend.set(key, JSON.stringify(value));
                } else if (value !== undefined && value !== null) {
                    formDataToSend.set(key, value.toString());
                }
            }

            const result = await updateExpressionAction(expression.documentId, formDataToSend);

            if (result.success) {
                toast.success("Expression modifiée avec succès !");
                router.push("/expressions/my-expressions");
            } else {
                toast.error(result.error || "Erreur lors de la modification de l'expression");
            }
        } catch (error) {
            console.error("Error updating expression:", error);
            toast.error("Erreur lors de la modification de l'expression");
        } finally {
            setIsLoading(false);
        }
    };

    const handleCancel = () => {
        router.push("/expressions/my-expressions");
    };

    // Data is passed from server, no loading state needed

    return (
        <div className="min-h-screen bg-gray-50">
            {/* Header */}
            <header className="bg-white shadow-sm border-b border-gray-200">
                <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="flex justify-between items-center h-16">
                        <div className="flex items-center">
                            <Link href="/dashboard">
                                <h1 className="text-2xl font-bold text-blue-600">PillarScan</h1>
                            </Link>
                            <span className="ml-4 text-gray-500">Modifier l'expression</span>
                        </div>
                        <Button variant="outline" onClick={handleCancel}>
                            Annuler
                        </Button>
                    </div>
                </div>
            </header>

            {/* Main Content */}
            <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
                <form onSubmit={handleSubmit} className="space-y-8">
                    {/* Progress Indicator */}
                    <div className="mb-8">
                        <div className="flex items-center justify-center space-x-4 text-sm">
                            <div className="flex items-center space-x-2">
                                <CheckCircleIcon className="w-5 h-5 text-green-500" />
                                <span className="text-green-600 font-medium">
                                    Modification en cours
                                </span>
                            </div>
                        </div>
                    </div>

                    {/* Basic Information */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center">
                                <InformationCircleIcon className="w-5 h-5 mr-2 text-blue-600" />
                                Informations de base
                            </CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-6">
                            {/* Title */}
                            <div>
                                <label
                                    htmlFor="titre"
                                    className="block text-sm font-medium text-gray-700 mb-2"
                                >
                                    Titre de votre expression *
                                </label>
                                <Input
                                    id="titre"
                                    name="titre"
                                    type="text"
                                    value={formData.titre}
                                    onChange={handleInputChange}
                                    placeholder="Décrivez votre préoccupation en quelques mots"
                                    required
                                    className="w-full"
                                />
                            </div>

                            {/* Content */}
                            <div>
                                <label
                                    htmlFor="contenu"
                                    className="block text-sm font-medium text-gray-700 mb-2"
                                >
                                    Description détaillée *
                                </label>
                                <textarea
                                    id="contenu"
                                    name="contenu"
                                    value={formData.contenu}
                                    onChange={handleInputChange}
                                    placeholder="Expliquez votre situation de manière détaillée..."
                                    required
                                    rows={6}
                                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                />
                            </div>

                            {/* Type and Urgency */}
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label
                                        htmlFor="type_expression"
                                        className="block text-sm font-medium text-gray-700 mb-2"
                                    >
                                        Type d'expression *
                                    </label>
                                    <select
                                        id="type_expression"
                                        name="type_expression"
                                        value={formData.type_expression}
                                        onChange={handleInputChange}
                                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                    >
                                        <option value="probleme">Problème</option>
                                        <option value="satisfaction">Satisfaction</option>
                                        <option value="idee">Idée d'amélioration</option>
                                        <option value="question">Question</option>
                                    </select>
                                </div>

                                <div>
                                    <label
                                        htmlFor="urgence"
                                        className="block text-sm font-medium text-gray-700 mb-2"
                                    >
                                        Niveau d'urgence *
                                    </label>
                                    <select
                                        id="urgence"
                                        name="urgence"
                                        value={formData.urgence}
                                        onChange={handleInputChange}
                                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                    >
                                        <option value={1}>1 - Très faible</option>
                                        <option value={2}>2 - Faible</option>
                                        <option value={3}>3 - Modéré</option>
                                        <option value={4}>4 - Élevé</option>
                                        <option value={5}>5 - Critique</option>
                                    </select>
                                </div>
                            </div>

                            {/* Emotional State */}
                            <div>
                                <label
                                    htmlFor="etat_emotionnel"
                                    className="block text-sm font-medium text-gray-700 mb-2"
                                >
                                    État émotionnel *
                                </label>
                                <select
                                    id="etat_emotionnel"
                                    name="etat_emotionnel"
                                    value={formData.etat_emotionnel}
                                    onChange={handleInputChange}
                                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                >
                                    <option value="neutre">Neutre</option>
                                    <option value="frustration">Frustration</option>
                                    <option value="colere">Colère</option>
                                    <option value="tristesse">Tristesse</option>
                                    <option value="espoir">Espoir</option>
                                    <option value="joie">Joie</option>
                                </select>
                            </div>

                            {/* Event Date */}
                            <div>
                                <label
                                    htmlFor="date_evenement"
                                    className="block text-sm font-medium text-gray-700 mb-2"
                                >
                                    <CalendarIcon className="w-4 h-4 inline mr-1" />
                                    Date de l'événement *
                                </label>
                                <Input
                                    id="date_evenement"
                                    name="date_evenement"
                                    type="date"
                                    value={formData.date_evenement}
                                    onChange={handleInputChange}
                                    required
                                    className="w-full"
                                />
                            </div>
                        </CardContent>
                    </Card>

                    {/* Location */}
                    <Card>
                        <CardHeader>
                            <CardTitle>Localisation</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <LocationPicker
                                onLocationSelect={handleLocationSelect}
                                defaultValue={location || undefined}
                            />
                        </CardContent>
                    </Card>

                    {/* Pillars Selection */}
                    <Card>
                        <CardHeader>
                            <CardTitle>Piliers de la société concernés *</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                                {piliers.map((pilier) => (
                                    <button
                                        key={pilier.documentId}
                                        type="button"
                                        onClick={() => togglePilier(pilier.documentId)}
                                        className={`p-3 rounded-lg border-2 transition-all ${
                                            selectedPiliers.includes(pilier.documentId)
                                                ? "border-blue-500 bg-blue-50"
                                                : "border-gray-200 hover:border-gray-300"
                                        }`}
                                    >
                                        <div className="flex items-center space-x-2">
                                            <PilierIcon
                                                iconName={pilier.icone}
                                                className="h-6 w-6"
                                                style={{ color: pilier.couleur }}
                                            />
                                            <span className="text-sm font-medium">
                                                {pilier.nom}
                                            </span>
                                        </div>
                                    </button>
                                ))}
                            </div>
                        </CardContent>
                    </Card>

                    {/* Sub-pillars */}
                    {filteredSousPiliers.length > 0 && (
                        <Card>
                            <CardHeader>
                                <CardTitle>Sous-piliers (optionnel)</CardTitle>
                            </CardHeader>
                            <CardContent>
                                <div className="flex flex-wrap gap-2">
                                    {filteredSousPiliers.map((sousPilier) => (
                                        <button
                                            key={sousPilier.documentId}
                                            type="button"
                                            onClick={() => toggleSousPilier(sousPilier.documentId)}
                                            className={`px-3 py-2 rounded-lg border text-sm font-medium transition-all ${
                                                selectedSousPiliers.includes(sousPilier.documentId)
                                                    ? "border-blue-500 bg-blue-50 text-blue-700"
                                                    : "border-gray-200 hover:border-gray-300 text-gray-700"
                                            }`}
                                        >
                                            {sousPilier.nom}
                                        </button>
                                    ))}
                                </div>
                            </CardContent>
                        </Card>
                    )}

                    {/* Entities */}
                    {entites.length > 0 && (
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center">
                                    <BuildingOfficeIcon className="w-5 h-5 mr-2 text-blue-600" />
                                    Entités concernées (optionnel)
                                </CardTitle>
                            </CardHeader>
                            <CardContent>
                                <div className="flex flex-wrap gap-2">
                                    {entites.map((entite) => (
                                        <button
                                            key={entite.documentId}
                                            type="button"
                                            onClick={() => toggleEntite(entite.documentId)}
                                            className={`px-3 py-2 rounded-lg border text-sm font-medium transition-all ${
                                                selectedEntites.includes(entite.documentId)
                                                    ? "border-blue-500 bg-blue-50 text-blue-700"
                                                    : "border-gray-200 hover:border-gray-300 text-gray-700"
                                            }`}
                                        >
                                            {entite.nom}
                                        </button>
                                    ))}
                                </div>
                            </CardContent>
                        </Card>
                    )}

                    {/* Tags */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center">
                                <TagIcon className="w-5 h-5 mr-2 text-blue-600" />
                                Mots-clés (optionnel)
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-3">
                                <div className="flex space-x-2">
                                    <Input
                                        type="text"
                                        value={tagInput}
                                        onChange={(e) => setTagInput(e.target.value)}
                                        placeholder="Ajouter un mot-clé"
                                        onKeyPress={(e) =>
                                            e.key === "Enter" && (e.preventDefault(), addTag())
                                        }
                                        className="flex-1"
                                    />
                                    <Button type="button" onClick={addTag} variant="outline">
                                        Ajouter
                                    </Button>
                                </div>
                                {tags.length > 0 && (
                                    <div className="flex flex-wrap gap-2">
                                        {tags.map((tag) => (
                                            <span
                                                key={tag}
                                                className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-blue-100 text-blue-800"
                                            >
                                                {tag}
                                                <button
                                                    type="button"
                                                    onClick={() => removeTag(tag)}
                                                    className="ml-2 hover:text-blue-600"
                                                >
                                                    <XMarkIcon className="w-4 h-4" />
                                                </button>
                                            </span>
                                        ))}
                                    </div>
                                )}
                            </div>
                        </CardContent>
                    </Card>

                    {/* Existing Media */}
                    {expression.medias && expression.medias.length > 0 && (
                        <Card>
                            <CardHeader>
                                <CardTitle>Pièces jointes existantes</CardTitle>
                                <p className="text-sm text-gray-500">
                                    Décochez les fichiers que vous souhaitez supprimer
                                </p>
                            </CardHeader>
                            <CardContent>
                                <div className="space-y-2">
                                    {expression.medias.map((media: any, index: number) => {
                                        const mediaId =
                                            typeof media === "object" ? media.id : media;

                                        const mediaUrl =
                                            typeof media === "object" ? assetUrl(media.url) : null;

                                        const mediaName =
                                            typeof media === "object"
                                                ? media.name
                                                : `Fichier ${index + 1}`;

                                        const isImage =
                                            typeof media === "object" &&
                                            media.mime?.startsWith("image/");

                                        return (
                                            <div
                                                key={mediaId}
                                                className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg border"
                                            >
                                                <input
                                                    type="checkbox"
                                                    checked={existingMediaToKeep.includes(mediaId)}
                                                    onChange={(e) => {
                                                        if (e.target.checked) {
                                                            setExistingMediaToKeep([
                                                                ...existingMediaToKeep,
                                                                mediaId,
                                                            ]);
                                                        } else {
                                                            setExistingMediaToKeep(
                                                                existingMediaToKeep.filter(
                                                                    (id) => id !== mediaId,
                                                                ),
                                                            );
                                                        }
                                                    }}
                                                    className="h-4 w-4 text-blue-600 rounded focus:ring-blue-500"
                                                />

                                                {/* Preview */}
                                                {isImage && mediaUrl ? (
                                                    <img
                                                        src={mediaUrl}
                                                        alt={mediaName}
                                                        className="h-12 w-12 object-cover rounded-lg"
                                                    />
                                                ) : (
                                                    <div className="h-12 w-12 bg-gray-200 rounded-lg flex items-center justify-center">
                                                        <DocumentIcon className="h-6 w-6 text-gray-500" />
                                                    </div>
                                                )}

                                                {/* File Info */}
                                                <div className="flex-1">
                                                    <p className="text-sm font-medium text-gray-900">
                                                        {mediaName}
                                                    </p>
                                                    {typeof media === "object" && media.size && (
                                                        <p className="text-xs text-gray-500">
                                                            {(media.size / 1024).toFixed(1)} KB
                                                        </p>
                                                    )}
                                                </div>

                                                {/* View Button */}
                                                {mediaUrl && (
                                                    <button
                                                        type="button"
                                                        onClick={() =>
                                                            window.open(mediaUrl, "_blank")
                                                        }
                                                        className="p-2 text-gray-500 hover:text-blue-600"
                                                    >
                                                        <EyeIcon className="h-4 w-4" />
                                                    </button>
                                                )}
                                            </div>
                                        );
                                    })}
                                </div>
                            </CardContent>
                        </Card>
                    )}

                    {/* New File Upload */}
                    <Card onClick={(e) => e.stopPropagation()}>
                        <CardHeader>
                            <CardTitle>Ajouter de nouvelles pièces jointes</CardTitle>
                            <p className="text-sm text-gray-500">
                                Vous pouvez ajouter jusqu'à {5 - files.length} nouveaux fichiers
                            </p>
                        </CardHeader>
                        <CardContent>
                            <FileUpload
                                onFilesChange={handleFilesChange}
                                maxFiles={5 - files.length}
                                maxSize={10} // 10MB
                                acceptedTypes={["image/*", ".pdf", ".doc", ".docx"]}
                            />
                        </CardContent>
                    </Card>

                    {/* Submit Button */}
                    <div className="flex space-x-4">
                        <Button
                            type="submit"
                            disabled={isLoading}
                            className="flex-1 bg-blue-600 hover:bg-blue-700 text-white"
                        >
                            {isLoading ? (
                                <>
                                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                                    Modification en cours...
                                </>
                            ) : (
                                "Modifier l'expression"
                            )}
                        </Button>
                        <Button
                            type="button"
                            variant="outline"
                            onClick={handleCancel}
                            disabled={isLoading}
                            className="px-8"
                        >
                            Annuler
                        </Button>
                    </div>
                </form>
            </main>
        </div>
    );
}
