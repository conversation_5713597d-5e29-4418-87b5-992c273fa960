import { redirect } from "next/navigation";
import { getCurrentUser } from "@/lib/auth/server";
import { fetchExpressionUserStats } from "@/lib/server-api";
import { Card } from "@/components/ui/Card";
import { LogoutButton, ProfileButton, QuickActions } from "./dashboard-client";
import { StatsClient } from "./stats-client";
import Layout from "@/components/layout/Layout";

export default async function DashboardPage() {
    const user = await getCurrentUser();

    if (!user) {
        redirect("/auth/login");
    }

    const profile = user.profile;

    // Fetch user statistics from the efficient endpoint
    let stats = {
        total_expressions: 0,
        by_status: {},
        by_type: {},
        by_urgency: {},
        resolution_rate: 0,
    };

    try {
        stats = await fetchExpressionUserStats();
    } catch (error) {
        console.error("Error fetching user stats:", error);
    }

    return (
        <Layout showHeader={true} showFooter={false}>
            {/* Main Content */}
            <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
                {/* Welcome Section */}
                <div className="mb-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-2">
                        Bienvenue sur votre tableau de bord
                    </h2>
                    <p className="text-gray-600">
                        Gérez vos expressions citoyennes et suivez leur impact sur la société
                        française.
                    </p>
                </div>

                {/* Real User Statistics */}
                <StatsClient stats={stats} />

                {/* Quick Actions - Moved to client component */}
                <QuickActions />

                {/* Profile Info */}
                <Card className="p-6">
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">
                        Informations du profil
                    </h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <p className="text-sm font-medium text-gray-600">Nom d'utilisateur</p>
                            <p className="text-gray-900">{user.username}</p>
                        </div>
                        <div>
                            <p className="text-sm font-medium text-gray-600">Email</p>
                            <p className="text-gray-900">{user.email}</p>
                        </div>
                        <div>
                            <p className="text-sm font-medium text-gray-600">Statut</p>
                            <p className="text-gray-900">{profile?.statut || "actif"}</p>
                        </div>
                        <div>
                            <p className="text-sm font-medium text-gray-600">Membre depuis</p>
                            <p className="text-gray-900">
                                {profile?.date_inscription
                                    ? new Date(profile.date_inscription).toLocaleDateString("fr-FR")
                                    : new Date(user.createdAt).toLocaleDateString("fr-FR")}
                            </p>
                        </div>
                    </div>

                    <div className="mt-4">
                        <ProfileButton />
                    </div>
                </Card>
            </main>
        </Layout>
    );
}
