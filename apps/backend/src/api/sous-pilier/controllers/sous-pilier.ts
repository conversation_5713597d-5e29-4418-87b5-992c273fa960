/**
 * sous-pilier controller
 */

import { factories } from "@strapi/strapi";

const getPopulate = (populate: any) => {
    if (populate === "*") {
        return "*";
    }
    if (populate) {
        return (populate as string).split(",");
    }

    return null;
};

export default factories.createCoreController("api::sous-pilier.sous-pilier", ({ strapi }) => ({
    /**
     * Override the default find method to handle pilier filtering
     */
    async find(ctx) {
        // Ensure ctx.query exists
        if (!ctx.query) {
            ctx.query = {};
        }

        // Extract query parameters with defaults
        const { filters = {}, populate, ...otherParams } = ctx.query as any;

        // Build the where clause
        const where: any = { actif: true };

        // Handle pilier filter
        if (filters && filters.pilier) {
            where.pilier = { id: filters.pilier };
        }

        // Fetch sous-piliers with filtering
        const sousPiliers = await strapi.documents("api::sous-pilier.sous-pilier").findMany({
            filters: where,
            populate: (getPopulate(populate) || ["pilier"]) as any,
            sort: { ordre: "asc" },
        });

        return { data: sousPiliers };
    },
}));
