/**
 * Profile controller
 */

import { factories } from "@strapi/strapi";

export default factories.createCoreController("api::profile.profile", ({ strapi }) => ({
    /**
     * Get current user's profile
     */
    async me(ctx) {
        if (!ctx.state.user) {
            return ctx.unauthorized("You must be authenticated");
        }

        const profile = await strapi.documents("api::profile.profile").findFirst({
            filters: { user: ctx.state.user.id },
            populate: ["lieu_residence", "validateur_assigne", "expressions"],
        });

        if (!profile) {
            return ctx.notFound("Profile not found");
        }

        return { data: profile };
    },

    /**
     * Update current user's profile
     */
    async updateMe(ctx) {
        if (!ctx.state.user) {
            return ctx.unauthorized("You must be authenticated");
        }

        const profile = await strapi.documents("api::profile.profile").findFirst({
            filters: { user: ctx.state.user.id },
        });

        if (!profile) {
            return ctx.notFound("Profile not found");
        }

        const { data } = ctx.request.body;

        // Remove fields that shouldn't be updated by user
        delete data.role;
        delete data.statut;
        delete data.nb_expressions;
        delete data.score_reputation;
        delete data.compte_verifie;
        delete data.date_inscription;
        delete data.user;

        const updatedProfile = await strapi.documents("api::profile.profile").update({
            documentId: profile.documentId,
            data: {
                ...data,
                derniere_activite: new Date(),
            },
            populate: ["lieu_residence", "validateur_assigne"],
        });

        return { data: updatedProfile };
    },

    /**
     * Get profile statistics
     */
    async getStats(ctx) {
        const { id } = ctx.params;

        // If no ID provided, use current user's profile
        let profileId = id;
        if (!profileId) {
            if (!ctx.state.user) {
                return ctx.unauthorized("You must be authenticated");
            }

            const profile = await strapi.documents("api::profile.profile").findFirst({
                filters: { user: { id: ctx.state.user.id } },
            });

            if (!profile) {
                return ctx.notFound("Profile not found");
            }

            profileId = profile.documentId;
        }

        // Get expression statistics
        const expressionStats = await strapi.documents("api::expression.expression").findMany({
            filters: { auteur: { documentId: profileId } },
            fields: ["statut", "type_expression", "urgence", "date_creation"],
        });

        // Calculate statistics
        const stats = {
            total_expressions: expressionStats.length,
            by_status: {},
            by_type: {},
            by_urgency: {},
            monthly_activity: {},
            resolution_rate: 0,
        };

        // Group by status
        expressionStats.forEach((expr) => {
            stats.by_status[expr.statut] = (stats.by_status[expr.statut] || 0) + 1;
            stats.by_type[expr.type_expression] = (stats.by_type[expr.type_expression] || 0) + 1;
            stats.by_urgency[expr.urgence] = (stats.by_urgency[expr.urgence] || 0) + 1;

            // Monthly activity
            const month = new Date(expr.date_creation).toISOString().slice(0, 7);
            stats.monthly_activity[month] = (stats.monthly_activity[month] || 0) + 1;
        });

        // Calculate resolution rate
        const resolvedCount = stats.by_status["resolu"] || 0;
        const publishedCount = (stats.by_status["publie"] || 0) + resolvedCount;
        stats.resolution_rate = publishedCount > 0 ? (resolvedCount / publishedCount) * 100 : 0;

        return { data: stats };
    },

    /**
     * Create profile for new user (called during registration)
     */
    async createForUser(ctx) {
        const { userId, profileData } = ctx.request.body;

        // Check if profile already exists
        const existingProfile = await strapi.documents("api::profile.profile").findFirst({
            filters: { user: { id: userId } },
        });

        if (existingProfile) {
            return ctx.badRequest("Profile already exists for this user");
        }

        const profile = await strapi.documents("api::profile.profile").create({
            data: {
                ...profileData,
                user: userId,
                role: "contributeur",
                statut: "actif",
                date_inscription: new Date(),
                nb_expressions: 0,
                score_reputation: 0.0,
                compte_verifie: false,
                preferences: {
                    notifications_email: true,
                    notifications_push: true,
                    langue: "fr",
                    theme: "auto",
                },
            },
            populate: ["lieu_residence"],
        });

        return { data: profile };
    },

    /**
     * Get leaderboard of most active contributors
     */
    async leaderboard(ctx) {
        const { limit = 10, period = "all" } = ctx.query;

        let dateFilter = {};
        if (period === "month") {
            const oneMonthAgo = new Date();
            oneMonthAgo.setMonth(oneMonthAgo.getMonth() - 1);
            dateFilter = { date_creation: { $gte: oneMonthAgo } };
        } else if (period === "week") {
            const oneWeekAgo = new Date();
            oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);
            dateFilter = { date_creation: { $gte: oneWeekAgo } };
        }

        // Get profiles with their expression counts
        const profiles = await strapi.documents("api::profile.profile").findMany({
            filters: {
                statut: "actif",
                compte_verifie: true,
            },
            populate: ["lieu_residence"],
            sort: { nb_expressions: "desc", score_reputation: "desc" },
            pagination: { pageSize: parseInt(limit as string) },
        });

        // For each profile, get recent expression count if period is specified
        if (period !== "all") {
            for (const profile of profiles) {
                const recentCount = await strapi.documents("api::expression.expression").count({
                    filters: {
                        auteur: { documentId: profile.documentId || String(profile.id) },
                        statut: { $in: ["publie", "resolu"] },
                        ...dateFilter,
                    },
                });
                (profile as any).recent_expressions = recentCount;
            }

            // Re-sort by recent activity
            profiles.sort(
                (a, b) =>
                    ((b as any).recent_expressions || 0) - ((a as any).recent_expressions || 0),
            );
        }

        return { data: profiles };
    },

    /**
     * Admin: Update user role
     */
    async updateRole(ctx) {
        const { id } = ctx.params;
        const { role } = ctx.request.body;

        // Check admin permissions
        const adminProfile = await strapi.documents("api::profile.profile").findFirst({
            filters: { user: { id: ctx.state.user.id } },
        });

        if (
            !adminProfile ||
            !["admin_regional", "admin_national", "super_admin"].includes(adminProfile.role)
        ) {
            return ctx.forbidden("Insufficient permissions");
        }

        const validRoles = [
            "observateur",
            "contributeur",
            "contributeur_verifie",
            "validateur",
            "validateur_senior",
            "admin_regional",
            "admin_national",
        ];

        if (!validRoles.includes(role)) {
            return ctx.badRequest("Invalid role");
        }

        // Super admin can assign any role, others have restrictions
        if (adminProfile.role !== "super_admin") {
            const restrictedRoles = ["admin_national", "super_admin"];
            if (restrictedRoles.includes(role)) {
                return ctx.forbidden("Cannot assign this role");
            }
        }

        const updatedProfile = await strapi.documents("api::profile.profile").update({
            documentId: id,
            data: {
                role,
                derniere_activite: new Date(),
            },
            populate: ["lieu_residence", "validateur_assigne"],
        });

        // If promoting to validator, create validator record
        if (["validateur", "validateur_senior"].includes(role)) {
            const existingValidator = await strapi
                .documents("api::validateur.validateur")
                .findFirst({
                    filters: { profile: { documentId: id } },
                });

            if (!existingValidator) {
                await strapi.documents("api::validateur.validateur").create({
                    data: {
                        profile: id,
                        niveau: role === "validateur_senior" ? "senior" : "junior",
                        quota_jour: 50,
                        actif: true,
                        date_nomination: new Date(),
                        statistiques: {
                            nb_validations_total: 0,
                            nb_validations_mois: 0,
                            taux_approbation: 0.0,
                            temps_moyen_validation: 0,
                            score_qualite: 0.0,
                        },
                    },
                });
            }
        }

        return { data: updatedProfile };
    },
}));
