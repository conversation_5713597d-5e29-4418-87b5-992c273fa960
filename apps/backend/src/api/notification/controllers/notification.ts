/**
 * Notification controller
 */

import { factories } from "@strapi/strapi";

export default factories.createCoreController("api::notification.notification", ({ strapi }) => ({
    /**
     * Get notifications for current user with filters
     */
    async find(ctx) {
        try {
            if (!ctx.state.user) {
                return ctx.unauthorized("Authentication required");
            }

            // Find the profile associated with the current user
            const profile = await strapi.documents("api::profile.profile").findFirst({
                filters: { user: ctx.state.user.id },
            });

            if (!profile) {
                return {
                    data: [],
                    meta: { pagination: { page: 1, pageSize: 25, pageCount: 0, total: 0 } },
                };
            }

            // Extract query parameters
            const {
                page = 1,
                pageSize = 25,
                type,
                category,
                read,
                timeRange,
                startDate,
                endDate,
            } = ctx.query;

            // Build where clause
            let where: any = {
                recipient: { documentId: profile.documentId },
            };

            // Add filters
            if (type) {
                where.type = type;
            }

            if (category) {
                where.category = category;
            }

            if (read !== undefined) {
                where.read = read === "true";
            }

            // Handle time range filter
            if (timeRange || (startDate && endDate)) {
                where.createdAt = {};

                if (timeRange) {
                    const now = new Date();
                    const start = new Date();

                    switch (timeRange) {
                        case "today":
                            start.setHours(0, 0, 0, 0);
                            break;
                        case "week":
                            start.setDate(now.getDate() - 7);
                            break;
                        case "month":
                            start.setMonth(now.getMonth() - 1);
                            break;
                    }

                    where.createdAt.$gte = start;
                    where.createdAt.$lte = now;
                } else {
                    if (startDate) where.createdAt.$gte = new Date(startDate as string);
                    if (endDate) where.createdAt.$lte = new Date(endDate as string);
                }
            }

            // Get total count
            const total = await strapi.documents("api::notification.notification").count({
                filters: where,
            });

            // Fetch notifications with pagination
            const notifications = await strapi
                .documents("api::notification.notification")
                .findMany({
                    filters: where,
                    populate: ["expression"],
                    sort: { createdAt: "desc" },
                    pagination: {
                        page: Number(page),
                        pageSize: Number(pageSize),
                    },
                });

            return {
                data: notifications,
                meta: {
                    pagination: {
                        page: Number(page),
                        pageSize: Number(pageSize),
                        pageCount: Math.ceil(total / Number(pageSize)),
                        total,
                    },
                },
            };
        } catch (error) {
            console.error("Error in notification find method:", error);
            return ctx.badRequest("Failed to fetch notifications");
        }
    },

    /**
     * Mark notification as read
     */
    async markAsRead(ctx) {
        try {
            const { id } = ctx.params;

            if (!ctx.state.user) {
                return ctx.unauthorized("Authentication required");
            }

            // Find the profile
            const profile = await strapi.documents("api::profile.profile").findFirst({
                filters: { user: ctx.state.user.id },
            });

            if (!profile) {
                return ctx.forbidden("Profile not found");
            }

            // Check if notification belongs to user
            const notification = await strapi
                .documents("api::notification.notification")
                .findFirst({
                    filters: {
                        id,
                        recipient: { documentId: profile.documentId },
                    },
                });

            if (!notification) {
                return ctx.notFound("Notification not found");
            }

            // Update notification
            const updated = await strapi.documents("api::notification.notification").update({
                documentId: id,
                data: { read: true },
            });

            return { data: updated };
        } catch (error) {
            console.error("Error marking notification as read:", error);
            return ctx.badRequest("Failed to update notification");
        }
    },

    /**
     * Mark all notifications as read
     */
    async markAllAsRead(ctx) {
        try {
            if (!ctx.state.user) {
                return ctx.unauthorized("Authentication required");
            }

            // Find the profile
            const profile = await strapi.documents("api::profile.profile").findFirst({
                filters: { user: ctx.state.user.id },
            });

            if (!profile) {
                return ctx.forbidden("Profile not found");
            }

            // Update all unread notifications
            await strapi.db.connection.raw(
                `
                UPDATE notifications 
                SET read = true, updated_at = NOW()
                WHERE recipient = ? AND read = false
            `,
                [profile.id || profile.documentId],
            );

            return { data: { message: "All notifications marked as read" } };
        } catch (error) {
            console.error("Error marking all notifications as read:", error);
            return ctx.badRequest("Failed to update notifications");
        }
    },

    /**
     * Get notification statistics
     */
    async getStats(ctx) {
        try {
            if (!ctx.state.user) {
                return ctx.unauthorized("Authentication required");
            }

            // Find the profile
            const profile = await strapi.documents("api::profile.profile").findFirst({
                filters: { user: ctx.state.user.id },
            });

            if (!profile) {
                return {
                    data: {
                        total: 0,
                        unread: 0,
                        byCategory: {},
                        byType: {},
                    },
                };
            }

            // Get total and unread count
            const [total, unread] = await Promise.all([
                strapi.documents("api::notification.notification").count({
                    filters: { recipient: { documentId: profile.documentId } },
                }),
                strapi.documents("api::notification.notification").count({
                    filters: { recipient: { documentId: profile.documentId }, read: false },
                }),
            ]);

            // Get counts by category
            const byCategory = await strapi.db.connection.raw(
                `
                SELECT category, COUNT(*) as count
                FROM notifications
                WHERE recipient = ?
                GROUP BY category
            `,
                [profile.id || profile.documentId],
            );

            // Get counts by type
            const byType = await strapi.db.connection.raw(
                `
                SELECT type, COUNT(*) as count
                FROM notifications
                WHERE recipient = ?
                GROUP BY type
            `,
                [profile.id || profile.documentId],
            );

            return {
                data: {
                    total,
                    unread,
                    byCategory: byCategory.reduce((acc: any, item: any) => {
                        acc[item.category] = parseInt(item.count);
                        return acc;
                    }, {}),
                    byType: byType.reduce((acc: any, item: any) => {
                        acc[item.type] = parseInt(item.count);
                        return acc;
                    }, {}),
                },
            };
        } catch (error) {
            console.error("Error getting notification stats:", error);
            return ctx.badRequest("Failed to get notification statistics");
        }
    },

    /**
     * Delete a notification
     */
    async delete(ctx) {
        try {
            const { id } = ctx.params;

            if (!ctx.state.user) {
                return ctx.unauthorized("Authentication required");
            }

            // Find the profile
            const profile = await strapi.documents("api::profile.profile").findFirst({
                filters: { user: ctx.state.user.id },
            });

            if (!profile) {
                return ctx.forbidden("Profile not found");
            }

            // Check if notification belongs to user
            const notification = await strapi
                .documents("api::notification.notification")
                .findFirst({
                    filters: {
                        id,
                        recipient: { documentId: profile.documentId },
                    },
                });

            if (!notification) {
                return ctx.notFound("Notification not found");
            }

            // Delete notification
            await strapi.documents("api::notification.notification").delete({
                documentId: id,
            });

            return { data: { message: "Notification deleted successfully" } };
        } catch (error) {
            console.error("Error deleting notification:", error);
            return ctx.badRequest("Failed to delete notification");
        }
    },

    /**
     * Create a notification (for internal use)
     */
    async create(ctx) {
        try {
            if (!ctx.state.user) {
                return ctx.unauthorized("Authentication required");
            }

            const {
                recipientId,
                title,
                message,
                type,
                category,
                expressionId,
                actionUrl,
                actionLabel,
                metadata,
            } = ctx.request.body.data;

            // Create notification using the service
            const notification = await strapi
                .service("api::notification.notification")
                .createNotification({
                    title,
                    message,
                    type,
                    category,
                    recipientId,
                    expressionId,
                    actionUrl,
                    actionLabel,
                    metadata,
                });

            return { data: notification };
        } catch (error) {
            console.error("Error creating notification:", error);
            return ctx.badRequest("Failed to create notification");
        }
    },
}));
