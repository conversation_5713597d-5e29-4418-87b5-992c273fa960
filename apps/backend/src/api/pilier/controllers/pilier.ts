/**
 * Pilier controller
 */

import { factories } from "@strapi/strapi";

const getPopulate = (populate: any) => {
    if (populate === "*") {
        return "*";
    }
    if (populate) {
        return (populate as string).split(",");
    }

    return null;
};

export default factories.createCoreController("api::pilier.pilier", ({ strapi }) => ({
    /**
     * Get all active pillars with their sub-pillars
     */
    async find(ctx) {
        const { populate } = ctx.query;

        const pillars = await strapi.documents("api::pilier.pilier").findMany({
            filters: { actif: true },
            populate: (getPopulate(populate) || ["sous_piliers"]) as any,
            sort: { ordre: "asc" },
        });

        return { data: pillars };
    },

    /**
     * Get a active pillar by ID
     */

    async findOne(ctx) {
        const { id } = ctx.params;
        const { populate } = ctx.query;

        const pillar = await strapi.documents("api::pilier.pilier").findOne({
            documentId: id,
            populate: (getPopulate(populate) || ["sous_piliers"]) as any,
        });

        if (!pillar) {
            return ctx.notFound("Pillar not found");
        }

        return { data: pillar };
    },

    /**
     * Get pillar statistics
     */
    async getStats(ctx) {
        const { id } = ctx.params;
        const { period = "all" } = ctx.query;

        const pillar = await strapi.documents("api::pilier.pilier").findOne({
            documentId: id,
            populate: ["sous_piliers"],
        });

        if (!pillar) {
            return ctx.notFound("Pillar not found");
        }

        // Build date filter
        let dateFilter = {};
        if (period === "month") {
            const oneMonthAgo = new Date();
            oneMonthAgo.setMonth(oneMonthAgo.getMonth() - 1);
            dateFilter = { date_creation: { $gte: oneMonthAgo } };
        } else if (period === "week") {
            const oneWeekAgo = new Date();
            oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);
            dateFilter = { date_creation: { $gte: oneWeekAgo } };
        }

        // Get expression statistics
        const expressions = await strapi.documents("api::expression.expression").findMany({
            filters: {
                piliers: { id: pillar.id },
                statut: { $in: ["publie", "resolu"] },
                ...dateFilter,
            },
            fields: ["statut", "type_expression", "urgence", "date_creation", "date_resolution"],
        });

        // Calculate statistics
        const stats = {
            total_expressions: expressions.length,
            by_status: {},
            by_type: {},
            by_urgency: {},
            resolution_rate: 0,
            avg_resolution_time: 0,
            monthly_trend: {},
        };

        let totalResolutionTime = 0;
        let resolvedCount = 0;

        expressions.forEach((expr) => {
            stats.by_status[expr.statut] = (stats.by_status[expr.statut] || 0) + 1;
            stats.by_type[expr.type_expression] = (stats.by_type[expr.type_expression] || 0) + 1;
            stats.by_urgency[expr.urgence] = (stats.by_urgency[expr.urgence] || 0) + 1;

            // Monthly trend
            const month = new Date(expr.date_creation).toISOString().slice(0, 7);
            stats.monthly_trend[month] = (stats.monthly_trend[month] || 0) + 1;

            // Resolution time calculation
            if (expr.statut === "resolu" && expr.date_resolution) {
                const resolutionTime =
                    new Date(expr.date_resolution).getTime() -
                    new Date(expr.date_creation).getTime();
                totalResolutionTime += resolutionTime;
                resolvedCount++;
            }
        });

        // Calculate resolution rate and average time
        const publishedCount = expressions.length;
        stats.resolution_rate =
            publishedCount > 0 ? ((stats.by_status["resolu"] || 0) / publishedCount) * 100 : 0;
        stats.avg_resolution_time =
            resolvedCount > 0
                ? Math.round(totalResolutionTime / resolvedCount / (1000 * 60 * 60 * 24))
                : 0; // in days

        // Get sub-pillar statistics
        const subPillarStats = {};
        for (const subPillar of pillar.sous_piliers) {
            const subExpressions = await strapi.documents("api::expression.expression").count({
                filters: {
                    sous_piliers: { id: subPillar.id },
                    statut: { $in: ["publie", "resolu"] },
                    ...dateFilter,
                },
            });
            subPillarStats[subPillar.code] = subExpressions;
        }

        (stats as any).sub_pillars = subPillarStats;

        return { data: stats };
    },

    /**
     * Get geographic distribution for a pillar
     */
    async getGeoDistribution(ctx) {
        const { id } = ctx.params;
        const { level = "region" } = ctx.query;

        const pillar = await strapi.documents("api::pilier.pilier").findOne({
            documentId: id,
        });

        if (!pillar) {
            return ctx.notFound("Pillar not found");
        }

        // Get expressions with location data
        const expressions = await strapi.documents("api::expression.expression").findMany({
            filters: {
                piliers: { id: pillar.id },
                statut: { $in: ["publie", "resolu"] },
            },
            populate: ["lieu"],
        });

        // Group by geographic level
        const distribution: any = {};
        expressions.forEach((expr) => {
            if (expr.lieu) {
                const key = (expr.lieu as any)[level as string] || "Non spécifié";
                distribution[key] = (distribution[key] || 0) + 1;
            }
        });

        // Convert to array and sort
        const sortedDistribution = Object.entries(distribution)
            .map(([location, count]) => ({ location, count: count as number }))
            .sort((a, b) => b.count - a.count);

        return { data: sortedDistribution };
    },

    /**
     * Get trending topics for a pillar
     */
    async getTrendingTopics(ctx) {
        const { id } = ctx.params;
        const { days = 7 } = ctx.query;

        const pillar = await strapi.documents("api::pilier.pilier").findOne({
            documentId: id,
        });

        if (!pillar) {
            return ctx.notFound("Pillar not found");
        }

        const daysAgo = new Date();
        daysAgo.setDate(daysAgo.getDate() - parseInt(days as string));

        // Get recent expressions
        const expressions = await strapi.documents("api::expression.expression").findMany({
            filters: {
                piliers: { id: pillar.id },
                statut: { $in: ["publie", "resolu"] },
                date_creation: { $gte: daysAgo },
            },
            fields: ["analyse_ia", "tags"],
        });

        // Extract and count keywords
        const keywordCounts: Record<string, number> = {};
        expressions.forEach((expr) => {
            // From AI analysis
            const analyseIa = expr.analyse_ia as any;
            if (analyseIa && analyseIa.mots_cles && Array.isArray(analyseIa.mots_cles)) {
                analyseIa.mots_cles.forEach((keyword: string) => {
                    keywordCounts[keyword] = (keywordCounts[keyword] || 0) + 1;
                });
            }

            // From tags
            if (expr.tags && Array.isArray(expr.tags)) {
                expr.tags.forEach((tag: any) => {
                    if (typeof tag === "string") {
                        keywordCounts[tag] = (keywordCounts[tag] || 0) + 1;
                    }
                });
            }
        });

        // Sort and return top keywords
        const trendingTopics = Object.entries(keywordCounts)
            .map(([keyword, count]) => ({ keyword, count: count as number }))
            .sort((a, b) => b.count - a.count)
            .slice(0, 20);

        return { data: trendingTopics };
    },
}));
