/**
 * PillarScan Permissions Setup Script
 *
 * This script configures all API permissions based on the role hierarchy:
 * - Public (no auth)
 * - Authenticated (any logged-in user)
 * - Role-based (specific roles)
 */

interface PermissionConfig {
    action: string;
    roles: string[];
}

interface ApiPermissions {
    [apiName: string]: {
        [controllerName: string]: {
            [actionName: string]: PermissionConfig;
        };
    };
}

// Define all API permissions based on the PillarScan specifications
// Note: "public" permissions are automatically included in "authenticated"
const API_PERMISSIONS: ApiPermissions = {
    // Profile API
    profile: {
        profile: {
            find: { action: "find", roles: ["public", "authenticated"] },
            findOne: { action: "findOne", roles: ["public", "authenticated"] },
            create: { action: "create", roles: ["authenticated"] },
            update: { action: "update", roles: ["authenticated"] },
            delete: { action: "delete", roles: ["authenticated"] },
            me: { action: "me", roles: ["authenticated"] },
            updateMe: { action: "updateMe", roles: ["authenticated"] },
            getStats: { action: "getStats", roles: ["authenticated"] },
            leaderboard: { action: "leaderboard", roles: ["public", "authenticated"] },
            updateRole: { action: "updateRole", roles: ["authenticated"] },
            createForUser: { action: "createForUser", roles: ["authenticated"] },
        },
    },

    // Expression API
    expression: {
        expression: {
            find: { action: "find", roles: ["public", "authenticated"] },
            findOne: { action: "findOne", roles: ["public", "authenticated"] },
            create: { action: "create", roles: ["authenticated"] },
            update: { action: "update", roles: ["authenticated"] },
            delete: { action: "delete", roles: ["authenticated"] },
            findPublic: { action: "findPublic", roles: ["public", "authenticated"] },
            submit: { action: "submit", roles: ["authenticated"] },
            moderationQueue: { action: "moderationQueue", roles: ["authenticated"] },
            validate: { action: "validate", roles: ["authenticated"] },
            getAnalytics: { action: "getAnalytics", roles: ["authenticated"] },
            getUserStats: { action: "getUserStats", roles: ["authenticated"] },
            getGlobalStats: { action: "getGlobalStats", roles: ["public", "authenticated"] },
            getModerationStats: { action: "getModerationStats", roles: ["authenticated"] },
        },
    },

    // Pilier API
    pilier: {
        pilier: {
            find: { action: "find", roles: ["public", "authenticated"] },
            findOne: { action: "findOne", roles: ["public", "authenticated"] },
            create: { action: "create", roles: ["authenticated"] },
            update: { action: "update", roles: ["authenticated"] },
            delete: { action: "delete", roles: ["authenticated"] },
        },
    },

    // Sous-Pilier API
    "sous-pilier": {
        "sous-pilier": {
            find: { action: "find", roles: ["public", "authenticated"] },
            findOne: { action: "findOne", roles: ["public", "authenticated"] },
            create: { action: "create", roles: ["authenticated"] },
            update: { action: "update", roles: ["authenticated"] },
            delete: { action: "delete", roles: ["authenticated"] },
        },
    },

    // Lieu API
    lieu: {
        lieu: {
            find: { action: "find", roles: ["public", "authenticated"] },
            findOne: { action: "findOne", roles: ["public", "authenticated"] },
            create: { action: "create", roles: ["authenticated"] },
            update: { action: "update", roles: ["authenticated"] },
            delete: { action: "delete", roles: ["authenticated"] },
            search: { action: "search", roles: ["public", "authenticated"] },
            getChildren: { action: "getChildren", roles: ["public", "authenticated"] },
            hierarchy: { action: "hierarchy", roles: ["public", "authenticated"] },
            createFromCoordinates: { action: "createFromCoordinates", roles: ["authenticated"] },
        },
    },

    // Entite API
    entite: {
        entite: {
            find: { action: "find", roles: ["public", "authenticated"] },
            findOne: { action: "findOne", roles: ["public", "authenticated"] },
            create: { action: "create", roles: ["authenticated"] },
            update: { action: "update", roles: ["authenticated"] },
            delete: { action: "delete", roles: ["authenticated"] },
        },
    },

    // Validateur API
    validateur: {
        validateur: {
            find: { action: "find", roles: ["authenticated"] },
            findOne: { action: "findOne", roles: ["authenticated"] },
            create: { action: "create", roles: ["authenticated"] },
            update: { action: "update", roles: ["authenticated"] },
            delete: { action: "delete", roles: ["authenticated"] },
        },
    },

    // Action API
    action: {
        action: {
            find: { action: "find", roles: ["public", "authenticated"] },
            findOne: { action: "findOne", roles: ["public", "authenticated"] },
            create: { action: "create", roles: ["authenticated"] },
            update: { action: "update", roles: ["authenticated"] },
            delete: { action: "delete", roles: ["authenticated"] },
        },
    },

    // Notification API
    notification: {
        notification: {
            find: { action: "find", roles: ["authenticated"] },
            findOne: { action: "findOne", roles: ["authenticated"] },
            create: { action: "create", roles: ["authenticated"] },
            update: { action: "update", roles: ["authenticated"] },
            delete: { action: "delete", roles: ["authenticated"] },
            markAsRead: { action: "markAsRead", roles: ["authenticated"] },
            markAllAsRead: { action: "markAllAsRead", roles: ["authenticated"] },
            getUnreadCount: { action: "getUnreadCount", roles: ["authenticated"] },
        },
    },

    // Perimetre API
    perimetre: {
        perimetre: {
            find: { action: "find", roles: ["authenticated"] },
            findOne: { action: "findOne", roles: ["authenticated"] },
            create: { action: "create", roles: ["authenticated"] },
            update: { action: "update", roles: ["authenticated"] },
            delete: { action: "delete", roles: ["authenticated"] },
        },
    },
};

// Plugin permissions (users-permissions plugin)
const PLUGIN_PERMISSIONS = {
    "users-permissions": {
        auth: {
            callback: { action: "callback", roles: ["public"] },
            connect: { action: "connect", roles: ["public"] },
            emailConfirmation: { action: "emailconfirmation", roles: ["public"] },
            forgotPassword: { action: "forgotpassword", roles: ["public"] },
            register: { action: "register", roles: ["public"] },
            resetPassword: { action: "resetpassword", roles: ["public"] },
            sendEmailConfirmation: { action: "sendemailconfirmation", roles: ["public"] },
        },
        user: {
            count: { action: "count", roles: ["authenticated"] },
            create: { action: "create", roles: ["authenticated"] },
            destroy: { action: "destroy", roles: ["authenticated"] },
            destroyAll: { action: "destroyall", roles: ["authenticated"] },
            find: { action: "find", roles: ["authenticated"] },
            findOne: { action: "findone", roles: ["authenticated"] },
            me: { action: "me", roles: ["authenticated"] },
            update: { action: "update", roles: ["authenticated"] },
        },
    },
    upload: {
        "content-api": {
            find: { action: "find", roles: ["public"] },
            findOne: { action: "findone", roles: ["public"] },
            upload: { action: "upload", roles: ["authenticated"] },
            destroy: { action: "destroy", roles: ["authenticated"] },
        },
    },
};

export async function setupPermissions(strapi: any) {
    try {
        // Get all roles
        const roles = await strapi.query("plugin::users-permissions.role").findMany({
            populate: ["permissions"],
        });

        const publicRole = roles.find((role: any) => role.type === "public");
        const authenticatedRole = roles.find((role: any) => role.type === "authenticated");

        if (!publicRole || !authenticatedRole) {
            throw new Error(
                "Default roles not found. Please ensure Strapi is properly initialized.",
            );
        }

        console.log("📋 Found roles:", roles.map((r: any) => r.type).join(", "));

        // Process API permissions
        for (const [apiName, controllers] of Object.entries(API_PERMISSIONS)) {
            for (const [controllerName, actions] of Object.entries(controllers)) {
                for (const [, config] of Object.entries(actions)) {
                    await updatePermission(
                        strapi,
                        `api::${apiName}.${controllerName}`,
                        config.action,
                        config.roles,
                        publicRole,
                        authenticatedRole,
                    );
                }
            }
        }

        // Process plugin permissions
        for (const [pluginName, controllers] of Object.entries(PLUGIN_PERMISSIONS)) {
            for (const [controllerName, actions] of Object.entries(controllers)) {
                for (const [, config] of Object.entries(actions)) {
                    await updatePermission(
                        strapi,
                        `plugin::${pluginName}.${controllerName}`,
                        (config as PermissionConfig).action,
                        (config as PermissionConfig).roles,
                        publicRole,
                        authenticatedRole,
                    );
                }
            }
        }

        console.log("✅ Permissions setup completed successfully!");
    } catch (error) {
        console.error("❌ Error setting up permissions:", error);
        throw error;
    }
}

async function updatePermission(
    strapi: any,
    controller: string,
    action: string,
    allowedRoles: string[],
    publicRole: any,
    authenticatedRole: any,
) {
    try {
        // Update permissions for each role
        const rolesToUpdate = [];

        if (allowedRoles.includes("public")) {
            rolesToUpdate.push(publicRole);
        }

        if (allowedRoles.includes("authenticated")) {
            rolesToUpdate.push(authenticatedRole);
        }

        for (const role of rolesToUpdate) {
            // Find existing permission
            const existingPermission = await strapi
                .query("plugin::users-permissions.permission")
                .findOne({
                    where: {
                        role: role.id,
                        action: `${controller}.${action}`,
                    },
                });

            if (existingPermission) {
                // Update existing permission
                await strapi.query("plugin::users-permissions.permission").update({
                    where: { id: existingPermission.id },
                    data: { enabled: true },
                });
            } else {
                // Create new permission
                await strapi.query("plugin::users-permissions.permission").create({
                    data: {
                        role: role.id,
                        action: `${controller}.${action}`,
                        enabled: true,
                    },
                });
            }
        }

        // Disable permission for roles not in allowedRoles
        const rolesToDisable = [];

        if (!allowedRoles.includes("public")) {
            rolesToDisable.push(publicRole);
        }

        if (!allowedRoles.includes("authenticated")) {
            rolesToDisable.push(authenticatedRole);
        }

        for (const role of rolesToDisable) {
            const existingPermission = await strapi
                .query("plugin::users-permissions.permission")
                .findOne({
                    where: {
                        role: role.id,
                        action: `${controller}.${action}`,
                    },
                });

            if (existingPermission && existingPermission.enabled) {
                await strapi.query("plugin::users-permissions.permission").update({
                    where: { id: existingPermission.id },
                    data: { enabled: false },
                });
            }
        }
    } catch (error) {
        console.error(`✗ Failed to update permission: ${controller}.${action}`, error);
    }
}

// Helper function to create custom roles based on PillarScan specifications
export async function createCustomRoles(strapi: any) {
    console.log("🎭 Creating custom roles for PillarScan...");

    const customRoles = [
        {
            name: "Contributeur Vérifié",
            description: "Contributeur avec compte vérifié et privilèges étendus",
            type: "contributeur_verifie",
        },
        {
            name: "Validateur",
            description: "Peut valider et modérer les expressions",
            type: "validateur",
        },
        {
            name: "Validateur Senior",
            description: "Validateur expérimenté avec privilèges de formation",
            type: "validateur_senior",
        },
        {
            name: "Admin Régional",
            description: "Administrateur au niveau régional",
            type: "admin_regional",
        },
        {
            name: "Admin National",
            description: "Administrateur au niveau national",
            type: "admin_national",
        },
        {
            name: "Super Admin",
            description: "Contrôle total du système",
            type: "super_admin",
        },
    ];

    for (const roleData of customRoles) {
        try {
            // Check if role already exists
            const existingRole = await strapi.query("plugin::users-permissions.role").findOne({
                where: { type: roleData.type },
            });

            if (!existingRole) {
                await strapi.query("plugin::users-permissions.role").create({
                    data: roleData,
                });
                console.log(`✓ Created role: ${roleData.name}`);
            }
        } catch (error) {
            console.error(`✗ Failed to create role: ${roleData.name}`, error);
        }
    }
}

// Export a function to check current permissions (useful for debugging)
export async function checkPermissions(strapi: any, roleName: string, controller?: string) {
    const role = await strapi.query("plugin::users-permissions.role").findOne({
        where: { type: roleName },
        populate: ["permissions"],
    });

    if (!role) {
        console.log(`Role "${roleName}" not found`);
        return;
    }

    const enabledPermissions = role.permissions
        .filter((p: any) => p.enabled)
        .filter((p: any) => !controller || p.action.includes(controller));

    console.log(
        `\nPermissions for ${roleName} role${controller ? ` (filtered by ${controller})` : ""}:`,
    );
    enabledPermissions.forEach((perm: any) => {
        console.log(`- ${perm.action}`);
    });
}
